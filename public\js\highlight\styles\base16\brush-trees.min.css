/*!
  Theme: Brush Trees
  Author: <PERSON> <<EMAIL>>
  License: ~ MIT (or more permissive) [via base16-schemes-source]
  Maintainer: @highlightjs/core-team
  Version: 2021.09.0
*/pre code.hljs{display:block;overflow-x:auto;padding:1em}code.hljs{padding:3px 5px}.hljs{color:#6d828e;background:#e3efef}.hljs ::selection,.hljs::selection{background-color:#b0c5c8;color:#6d828e}.hljs-comment{color:#98afb5}.hljs-tag{color:#8299a1}.hljs-operator,.hljs-punctuation,.hljs-subst{color:#6d828e}.hljs-operator{opacity:.7}.hljs-bullet,.hljs-deletion,.hljs-name,.hljs-selector-tag,.hljs-template-variable,.hljs-variable{color:#b38686}.hljs-attr,.hljs-link,.hljs-literal,.hljs-number,.hljs-symbol,.hljs-variable.constant_{color:#d8bba2}.hljs-class .hljs-title,.hljs-title,.hljs-title.class_{color:#aab386}.hljs-strong{font-weight:700;color:#aab386}.hljs-addition,.hljs-code,.hljs-string,.hljs-title.class_.inherited__{color:#87b386}.hljs-built_in,.hljs-doctag,.hljs-keyword.hljs-atrule,.hljs-quote,.hljs-regexp{color:#86b3b3}.hljs-attribute,.hljs-function .hljs-title,.hljs-section,.hljs-title.function_,.ruby .hljs-property{color:#868cb3}.diff .hljs-meta,.hljs-keyword,.hljs-template-tag,.hljs-type{color:#b386b2}.hljs-emphasis{color:#b386b2;font-style:italic}.hljs-meta,.hljs-meta .hljs-keyword,.hljs-meta .hljs-string{color:#b39f9f}.hljs-meta .hljs-keyword,.hljs-meta-keyword{font-weight:700}