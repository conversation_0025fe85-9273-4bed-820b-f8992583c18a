{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../lib/box/index.ts"], "names": [], "mappings": ";;;;;AAAA,+BAAiC;AACjC,wDAAuC;AACvC,kDAA0B;AAC1B,yCAAoD;AACpD,qCAAiE;AACjE,2CAAqC;AACrC,mCAAsC;AACtC,2CAA6C;AAI7C,MAAM,cAAc,GAAG,IAAI,mBAAO,CAAC,GAAG,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AAO/C,MAAM,GAAI,SAAQ,qBAAY;IAa5B,YAAY,GAAS,EAAE,IAAY,EAAE,OAAgB;QACnD,KAAK,EAAE,CAAC;QAER,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC;YAC3B,UAAU,EAAE,IAAI;YAChB,gBAAgB,EAAE;gBAChB,kBAAkB,EAAE,GAAG;aACxB;SACF,EAAE,OAAO,CAAC,CAAC;QAEZ,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,UAAG,CAAC,EAAE,CAAC;YACxB,IAAI,IAAI,UAAG,CAAC;QACd,CAAC;QAED,IAAI,CAAC,OAAO,GAAG,GAAG,CAAC;QACnB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,IAAI,CAAC,UAAU,GAAG,EAAE,CAAC;QACrB,IAAI,CAAC,gBAAgB,GAAG,EAAE,CAAC;QAC3B,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;QACpB,IAAI,CAAC,KAAK,GAAG,GAAG,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;QAChC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,gBAAgB,EAAE,CAAC;QACpC,IAAI,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,IAAI,EAAE,CAAC;QACzC,IAAI,GAAG,CAAC,MAAM,CAAC,MAAM,IAAI,GAAG,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC;YAClD,OAAO,GAAG,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;QAC9C,CAAC;QACD,IAAI,CAAC,MAAM,GAAG,OAAO,CAAC;QACtB,IAAI,CAAC,OAAO,CAAC,OAAO,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,QAAQ,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAC3E,CAAC;IAED,gBAAgB;QACd,MAAM,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC;QAEzB,MAAM,KAAM,SAAQ,cAAI;YAGtB,MAAM,CAAC,OAAgB;gBACrB,OAAO,GAAG,CAAC,MAAM,CAAC,MAAM,CAAC;oBACvB,IAAI,EAAE,IAAI,CAAC,MAAM;iBAClB,EAAE,OAAO,CAAC,CAAC;YACd,CAAC;YAED,UAAU,CAAC,OAAgB;gBACzB,OAAO,GAAG,CAAC,MAAM,CAAC,UAAU,CAAC;oBAC3B,IAAI,EAAE,IAAI,CAAC,MAAM;iBAClB,EAAE,OAAO,CAAC,CAAC;YACd,CAAC;SACF;QAED,KAAK,CAAC,SAAS,CAAC,GAAG,GAAG,IAAI,CAAC;QAE3B,OAAO,KAAK,CAAC;IACf,CAAC;IAID,YAAY,CAAC,OAA8D,EAAE,EAA4B;QACvG,IAAI,CAAC,EAAE,IAAI,OAAO,OAAO,KAAK,UAAU,EAAE,CAAC;YACzC,EAAE,GAAG,OAAO,CAAC;YACb,OAAO,GAAG,cAAc,CAAC;QAC3B,CAAC;QAED,IAAI,OAAO,EAAE,KAAK,UAAU;YAAE,MAAM,IAAI,SAAS,CAAC,uBAAuB,CAAC,CAAC;QAC3E,IAAI,CAAC,CAAC,OAAO,YAAY,mBAAO,CAAC;YAAE,OAAO,GAAG,IAAI,mBAAO,CAAC,OAAO,CAAC,CAAC;QAElE,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC;YACnB,OAAO;YACP,OAAO,EAAE,EAAE;SACZ,CAAC,CAAC;IACL,CAAC;IAED,QAAQ,CAAC,IAAY,EAAE,MAAM,GAAG,EAAE;QAChC,MAAM,EAAE,OAAO,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;QAC9B,MAAM,OAAO,GAAG,EAAE,CAAC;QACnB,OAAO,aAAa,CAAC,GAAG,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,CAAC,MAAM,EAAE,MAAM,CAAC;aAC1D,MAAM,CAAC,OAAO,CAAC;aACf,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC;aACxC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;IAC5E,CAAC;IAED,gBAAgB,CAAC,IAAY;QAC3B,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;QACrC,MAAM,GAAG,GAAG,IAAA,WAAI,EAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;QAElC,OAAO,KAAK,CAAC,WAAW,CACtB,eAAe,CAAC,GAAG,CAAC,SAAS,CAAC,GAAG,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,EACnD,GAAG,EAAE,CAAC,OAAO,CAAC,GAAG,CAAC,EAClB,GAAG,EAAE,CAAC,IAAA,cAAI,EAAC,GAAG,CAAC,CAChB,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;YAChB,IAAI,EAAE,MAAM,CAAC,IAAI;YACjB,IAAI;SACL,CAAC,CAAC,CAAC;IACN,CAAC;IAED,OAAO,CAAC,QAAkC;QACxC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;QAE3C,OAAO,IAAA,cAAI,EAAC,IAAI,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;YAC7B,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE;gBAAE,OAAO;YAEjC,gCAAgC;YAChC,MAAM,YAAY,GAAG,eAAe,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC;YAC1E,MAAM,UAAU,GAAG,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,YAAY,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC;YAEhI,uBAAuB;YACvB,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;iBACvB,IAAI,CAAC,CAAC,KAAe,EAAE,EAAE,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,IAAY,EAAE,EAAE,CAAC,CAAC,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;iBACrF,GAAG,CAAC,CAAC,IAAY,EAAE,EAAE,CAAC,IAAI,CAAC,YAAY,CAAC,cAAI,CAAC,WAAW,EAAE,IAAI,CAAqB,CAAC,CAAC;QAC1F,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE;YACb,IAAI,GAAG,IAAI,GAAG,CAAC,IAAI,KAAK,QAAQ;gBAAE,MAAM,GAAG,CAAC;QAC9C,CAAC,CAAC,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;IAC1B,CAAC;IAED,YAAY,CAAC,IAAY,EAAE,IAAY;QACrC,IAAI,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,EAAE,CAAC;YAChC,OAAO,kBAAe,CAAC,OAAO,EAAE,CAAC;QACnC,CAAC;QAED,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC;QAEnC,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;QAE1C,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE;YACzB,IAAI;YACJ,IAAI;SACL,CAAC,CAAC;QAEH,OAAO,kBAAe,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC,KAAK,EAAE,SAAS,EAAE,EAAE;YAClE,MAAM,MAAM,GAAG,SAAS,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;YAC7C,IAAI,CAAC,MAAM;gBAAE,OAAO,KAAK,CAAC;YAE1B,MAAM,IAAI,GAAG,IAAI,IAAI,CAAC;gBACpB,kEAAkE;gBAClE,MAAM,EAAE,IAAA,WAAI,EAAC,IAAI,EAAE,IAAI,CAAC;gBACxB,4DAA4D;gBAC5D,IAAI,EAAE,eAAe,CAAC,IAAI,CAAC;gBAC3B,MAAM;gBACN,IAAI;aACL,CAAC,CAAC;YAEH,OAAO,OAAO,CAAC,KAAK,CAAC,kBAAe,CAAC,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,CAAC;iBACzE,UAAU,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;QAC3B,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;YACjB,IAAI,KAAK,EAAE,CAAC;gBACV,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,eAAe,EAAE,IAAA,oBAAO,EAAC,IAAI,CAAC,CAAC,CAAC;YAChD,CAAC;YAED,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;gBACxB,IAAI;gBACJ,IAAI;aACL,CAAC,CAAC;QACL,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE;YACb,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,GAAG,EAAE,EAAE,oBAAoB,EAAE,IAAA,oBAAO,EAAC,IAAI,CAAC,CAAC,CAAC;QAC9D,CAAC,CAAC,CAAC,OAAO,CAAC,GAAG,EAAE;YACd,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC;QACtC,CAAC,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;IACtB,CAAC;IAED,KAAK,CAAC,QAAoC;QACxC,IAAI,IAAI,CAAC,UAAU,EAAE,EAAE,CAAC;YACtB,OAAO,kBAAe,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC,8BAA8B,CAAC,CAAC,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;QAChG,CAAC;QAED,MAAM,EAAE,IAAI,EAAE,GAAG,IAAI,CAAC;QAEtB,SAAS,OAAO,CAAC,IAAI;YACnB,OAAO,eAAe,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;QACtD,CAAC;QAED,OAAO,IAAI,CAAC,OAAO,EAAE,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,IAAA,eAAK,EAAC,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE;YACzE,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;YAEvB,OAAO,CAAC,EAAE,CAAC,KAAK,EAAE,IAAI,CAAC,EAAE;gBACvB,IAAI,CAAC,YAAY,CAAC,cAAI,CAAC,WAAW,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC;YACrD,CAAC,CAAC,CAAC;YAEH,OAAO,CAAC,EAAE,CAAC,QAAQ,EAAE,IAAI,CAAC,EAAE;gBAC1B,IAAI,CAAC,YAAY,CAAC,cAAI,CAAC,WAAW,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC;YACrD,CAAC,CAAC,CAAC;YAEH,OAAO,CAAC,EAAE,CAAC,QAAQ,EAAE,IAAI,CAAC,EAAE;gBAC1B,IAAI,CAAC,YAAY,CAAC,cAAI,CAAC,WAAW,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC;YACrD,CAAC,CAAC,CAAC;YAEH,OAAO,CAAC,EAAE,CAAC,QAAQ,EAAE,IAAI,CAAC,EAAE;gBAC1B,IAAI,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;gBAC3B,IAAI,MAAM;oBAAE,MAAM,IAAI,GAAG,CAAC;gBAE1B,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;YAC9B,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;IAC1B,CAAC;IAED,OAAO;QACL,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;YAAE,OAAO;QAE/B,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC;QACrB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;IACtB,CAAC;IAED,UAAU;QACR,OAAO,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IAC/B,CAAC;CACF;AAED,SAAS,eAAe,CAAC,IAAY;IACnC,iCAAiC;IACjC,OAAO,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;AAClC,CAAC;AAED,SAAS,OAAO,CAAC,IAAY;IAC3B,MAAM,GAAG,GAAG,IAAA,0BAAgB,EAAC,IAAI,CAAC,CAAC;IACnC,MAAM,MAAM,GAAG,IAAA,0BAAc,GAAE,CAAC;IAEhC,MAAM,eAAe,GAAG,IAAI,kBAAe,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;QAC9D,GAAG,CAAC,IAAI,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;QAC1B,GAAG,CAAC,IAAI,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;IAC3B,CAAC,CAAC,CAAC;IAEH,GAAG,CAAC,EAAE,CAAC,MAAM,EAAE,KAAK,CAAC,EAAE,GAAG,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAEnD,OAAO,eAAe,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;AAC1D,CAAC;AAED,SAAS,QAAQ,CAAC,GAAS,EAAE,GAAW;IACtC,IAAI,CAAC,GAAG;QAAE,OAAO,IAAI,CAAC;IACtB,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE,CAAC;QAC5B,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,6EAA6E,CAAC,CAAC;QAC5F,OAAO,IAAI,CAAC;IACd,CAAC;IACD,MAAM,MAAM,GAAG,IAAA,mBAAM,EAAC,GAAG,CAAC,CAAC;IAC3B,IAAI,CAAC,MAAM,EAAE,CAAC;QACZ,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,+EAA+E,GAAG,GAAG,CAAC,CAAC;QACpG,OAAO,IAAI,CAAC;IACd,CAAC;IACD,OAAO,MAAM,CAAC;AAChB,CAAC;AAED,SAAS,aAAa,CAAC,IAAY,EAAE,MAAsB;IACzD,OAAO,IAAI,IAAI,MAAM,IAAI,MAAM,CAAC,MAAM,IAAI,IAAA,oBAAO,EAAC,IAAI,EAAE,MAAM,CAAC,CAAC;AAClE,CAAC;AAED,SAAS,aAAa,CAAC,GAAS,EAAE,IAAY,EAAE,OAAc,EAAE,MAAW,EAAE,MAAc;IACzF,IAAI,aAAa,CAAC,IAAI,EAAE,MAAM,CAAC;QAAE,OAAO,kBAAe,CAAC,OAAO,EAAE,CAAC;IAElE,OAAO,kBAAe,CAAC,GAAG,CAAC,IAAA,iBAAO,EAAC,IAAI,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE;QACnD,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,GAAG,EAAE,EAAE,8BAA8B,EAAE,IAAI,CAAC,CAAC;QAC7D,IAAI,GAAG,IAAI,GAAG,CAAC,IAAI,KAAK,QAAQ;YAAE,OAAO,EAAE,CAAC;QAC5C,MAAM,GAAG,CAAC;IACZ,CAAC,CAAC,EAAE,KAAK,EAAC,IAAI,EAAC,EAAE;QACf,MAAM,QAAQ,GAAG,IAAA,WAAI,EAAC,IAAI,EAAE,IAAI,CAAC,CAAC;QAClC,MAAM,KAAK,GAAG,MAAM,IAAA,cAAI,EAAC,QAAQ,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE;YAC7C,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,GAAG,EAAE,EAAE,yBAAyB,EAAE,QAAQ,CAAC,CAAC;YAC5D,IAAI,GAAG,IAAI,GAAG,CAAC,IAAI,KAAK,QAAQ;gBAAE,OAAO,IAAI,CAAC;YAC9C,MAAM,GAAG,CAAC;QACZ,CAAC,CAAC,CAAC;QACH,MAAM,UAAU,GAAG,GAAG,MAAM,GAAG,IAAI,EAAE,CAAC;QACtC,IAAI,KAAK,EAAE,CAAC;YACV,IAAI,KAAK,CAAC,WAAW,EAAE,EAAE,CAAC;gBACxB,OAAO,aAAa,CAAC,GAAG,EAAE,QAAQ,EAAE,OAAO,EAAE,MAAM,EAAE,GAAG,UAAU,GAAG,CAAC,CAAC;YACzE,CAAC;YACD,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE,MAAM,CAAC,EAAE,CAAC;gBACrC,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YAC3B,CAAC;QACH,CAAC;IACH,CAAC,CAAC,CAAC;AACL,CAAC;AAQD,kBAAe,GAAG,CAAC"}