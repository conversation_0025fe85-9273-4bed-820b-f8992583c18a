declare const _default: {
    title: string;
    subtitle: string;
    description: string;
    author: string;
    language: string;
    timezone: string;
    url: string;
    root: string;
    permalink: string;
    permalink_defaults: {};
    pretty_urls: {
        trailing_index: boolean;
        trailing_html: boolean;
    };
    source_dir: string;
    public_dir: string;
    tag_dir: string;
    archive_dir: string;
    category_dir: string;
    code_dir: string;
    i18n_dir: string;
    skip_render: any[];
    new_post_name: string;
    default_layout: string;
    titlecase: boolean;
    external_link: {
        enable: boolean;
        field: string;
        exclude: string;
    };
    filename_case: number;
    render_drafts: boolean;
    post_asset_folder: boolean;
    relative_link: boolean;
    future: boolean;
    syntax_highlighter: string;
    highlight: {
        auto_detect: boolean;
        line_number: boolean;
        tab_replace: string;
        wrap: boolean;
        exclude_languages: any[];
        language_attr: boolean;
        hljs: boolean;
        line_threshold: number;
        first_line_number: string;
        strip_indent: boolean;
    };
    prismjs: {
        preprocess: boolean;
        line_number: boolean;
        tab_replace: string;
        exclude_languages: any[];
        strip_indent: boolean;
    };
    use_filename_as_post_title: boolean;
    default_category: string;
    category_map: {};
    tag_map: {};
    date_format: string;
    time_format: string;
    updated_option: string;
    per_page: number;
    pagination_dir: string;
    theme: string;
    server: {
        cache: boolean;
    };
    deploy: {};
    ignore: any[];
    meta_generator: boolean;
};
export = _default;
