{"version": 3, "file": "fast-equals.esm.js", "sources": ["../src/utils.ts", "../src/comparator.ts", "../src/index.ts"], "sourcesContent": ["import type { EqualityComparator, InternalEqualityComparator } from './types';\n\ninterface Cache {\n  delete: (key: object) => void;\n  get: (key: object) => object | undefined;\n  set: (key: object, value: object) => void;\n}\n\nconst HAS_WEAK_MAP_SUPPORT = typeof WeakMap === 'function';\n\nconst { keys } = Object;\n\n/**\n * are the values passed strictly equal or both NaN\n *\n * @param a the value to compare against\n * @param b the value to test\n * @returns are the values equal by the SameValueZero principle\n */\nexport function sameValueZeroEqual(a: any, b: any) {\n  return a === b || (a !== a && b !== b);\n}\n\n/**\n * is the value a plain object\n *\n * @param value the value to test\n * @returns is the value a plain object\n */\nexport function isPlainObject(value: any) {\n  return value.constructor === Object || value.constructor == null;\n}\n\n/**\n * is the value promise-like (meaning it is thenable)\n *\n * @param value the value to test\n * @returns is the value promise-like\n */\nexport function isPromiseLike(value: any) {\n  return !!value && typeof value.then === 'function';\n}\n\n/**\n * is the value passed a react element\n *\n * @param value the value to test\n * @returns is the value a react element\n */\nexport function isReactElement(value: any) {\n  return !!(value && value.$$typeof);\n}\n\n/**\n * in cases where WeakMap is not supported, creates a new custom\n * object that mimics the necessary API aspects for cache purposes\n *\n * @returns the new cache object\n */\nexport function getNewCacheFallback(): Cache {\n  const entries: [object, object][] = [];\n\n  return {\n    delete(key: object) {\n      for (let index = 0; index < entries.length; ++index) {\n        if (entries[index][0] === key) {\n          entries.splice(index, 1);\n          return;\n        }\n      }\n    },\n\n    get(key: object) {\n      for (let index = 0; index < entries.length; ++index) {\n        if (entries[index][0] === key) {\n          return entries[index][1];\n        }\n      }\n    },\n\n    set(key: object, value: object) {\n      for (let index = 0; index < entries.length; ++index) {\n        if (entries[index][0] === key) {\n          entries[index][1] = value;\n          return;\n        }\n      }\n\n      entries.push([key, value]);\n    }\n  };\n}\n\n/**\n * get a new cache object to prevent circular references\n *\n * @returns the new cache object\n */\nexport const getNewCache = ((canUseWeakMap: boolean) => {\n  if (canUseWeakMap) {\n    return function _getNewCache(): Cache {\n      return new WeakMap();\n    };\n  }\n\n  return getNewCacheFallback;\n})(HAS_WEAK_MAP_SUPPORT);\n\n/**\n * create a custom isEqual handler specific to circular objects\n *\n * @param [isEqual] the isEqual comparator to use instead of isDeepEqual\n * @returns the method to create the `isEqual` function\n */\nexport function createCircularEqualCreator(isEqual?: EqualityComparator) {\n  return function createCircularEqual(\n    comparator: EqualityComparator,\n  ): InternalEqualityComparator {\n    const _comparator = isEqual || comparator;\n\n    return function circularEqual(\n      a,\n      b,\n      indexOrKeyA,\n      indexOrKeyB,\n      parentA,\n      parentB,\n      cache: Cache = getNewCache(),\n    ) {\n      const isCacheableA = !!a && typeof a === 'object';\n      const isCacheableB = !!b && typeof b === 'object';\n\n      if (isCacheableA !== isCacheableB) {\n        return false;\n      }\n\n      if (!isCacheableA && !isCacheableB) {\n        return _comparator(a, b, cache);\n      }\n\n      const cachedA = cache.get(a);\n      \n      if(cachedA && cache.get(b)) {\n        return cachedA === b;\n      }\n\n      cache.set(a, b);\n      cache.set(b, a);\n\n      const result = _comparator(a, b, cache);\n\n      cache.delete(a);\n      cache.delete(b);\n\n      return result;\n    };\n  };\n}\n\n/**\n * are the arrays equal in value\n *\n * @param a the array to test\n * @param b the array to test against\n * @param isEqual the comparator to determine equality\n * @param meta the meta object to pass through\n * @returns are the arrays equal\n */\nexport function areArraysEqual(\n  a: any[],\n  b: any[],\n  isEqual: InternalEqualityComparator,\n  meta: any,\n) {\n  let index = a.length;\n\n  if (b.length !== index) {\n    return false;\n  }\n\n  while (index-- > 0) {\n    if (!isEqual(a[index], b[index], index, index, a, b, meta)) {\n      return false;\n    }\n  }\n\n  return true;\n}\n\n/**\n * are the maps equal in value\n *\n * @param a the map to test\n * @param b the map to test against\n * @param isEqual the comparator to determine equality\n * @param meta the meta map to pass through\n * @returns are the maps equal\n */\nexport function areMapsEqual(\n  a: Map<any, any>,\n  b: Map<any, any>,\n  isEqual: InternalEqualityComparator,\n  meta: any,\n) {\n  let isValueEqual = a.size === b.size;\n\n  if (isValueEqual && a.size) {\n    const matchedIndices: Record<number, true> = {};\n    let indexA = 0;\n\n    a.forEach((aValue, aKey) => {\n      if (isValueEqual) {\n        let hasMatch = false;\n        let matchIndexB = 0;\n\n        b.forEach((bValue, bKey) => {\n          if (!hasMatch && !matchedIndices[matchIndexB]) {\n            hasMatch =\n              isEqual(aKey, bKey, indexA, matchIndexB, a, b, meta) &&\n              isEqual(aValue, bValue, aKey, bKey, a, b, meta);\n\n            if (hasMatch) {\n              matchedIndices[matchIndexB] = true;\n            }\n          }\n\n          matchIndexB++;\n        });\n\n        indexA++;\n        isValueEqual = hasMatch;\n      }\n    });\n  }\n\n  return isValueEqual;\n}\n\ntype Dictionary<Type> = {\n  [key: string]: Type;\n  [index: number]: Type;\n};\n\nconst OWNER = '_owner';\n\nconst hasOwnProperty = Function.prototype.bind.call(\n  Function.prototype.call,\n  Object.prototype.hasOwnProperty,\n);\n\n/**\n * are the objects equal in value\n *\n * @param a the object to test\n * @param b the object to test against\n * @param isEqual the comparator to determine equality\n * @param meta the meta object to pass through\n * @returns are the objects equal\n */\nexport function areObjectsEqual(\n  a: Dictionary<any>,\n  b: Dictionary<any>,\n  isEqual: InternalEqualityComparator,\n  meta: any,\n) {\n  const keysA = keys(a);\n\n  let index = keysA.length;\n\n  if (keys(b).length !== index) {\n    return false;\n  }\n\n  if (index) {\n    let key: string;\n\n    while (index-- > 0) {\n      key = keysA[index];\n\n      if (key === OWNER) {\n        const reactElementA = isReactElement(a);\n        const reactElementB = isReactElement(b);\n\n        if (\n          (reactElementA || reactElementB) &&\n          reactElementA !== reactElementB\n        ) {\n          return false;\n        }\n      }\n\n      if (\n        !hasOwnProperty(b, key) ||\n        !isEqual(a[key], b[key], key, key, a, b, meta)\n      ) {\n        return false;\n      }\n    }\n  }\n\n  return true;\n}\n\n/**\n * are the regExps equal in value\n *\n * @param a the regExp to test\n * @param b the regExp to test agains\n * @returns are the regExps equal\n */\nexport const areRegExpsEqual = (() => {\n  if (/foo/g.flags === 'g') {\n    return function areRegExpsEqual(a: RegExp, b: RegExp) {\n      return a.source === b.source && a.flags === b.flags;\n    };\n  }\n\n  return function areRegExpsEqualFallback(a: RegExp, b: RegExp) {\n    return (\n      a.source === b.source &&\n      a.global === b.global &&\n      a.ignoreCase === b.ignoreCase &&\n      a.multiline === b.multiline &&\n      a.unicode === b.unicode &&\n      a.sticky === b.sticky &&\n      a.lastIndex === b.lastIndex\n    );\n  };\n})();\n\n/**\n * are the sets equal in value\n *\n * @param a the set to test\n * @param b the set to test against\n * @param isEqual the comparator to determine equality\n * @param meta the meta set to pass through\n * @returns are the sets equal\n */\nexport function areSetsEqual(\n  a: Set<any>,\n  b: Set<any>,\n  isEqual: InternalEqualityComparator,\n  meta: any,\n) {\n  let isValueEqual = a.size === b.size;\n\n  if (isValueEqual && a.size) {\n    const matchedIndices: Record<number, true> = {};\n\n    a.forEach((aValue, aKey) => {\n      if (isValueEqual) {\n        let hasMatch = false;\n        let matchIndex = 0;\n\n        b.forEach((bValue, bKey) => {\n          if (!hasMatch && !matchedIndices[matchIndex]) {\n            hasMatch = isEqual(aValue, bValue, aKey, bKey, a, b, meta);\n\n            if (hasMatch) {\n              matchedIndices[matchIndex] = true;\n            }\n          }\n\n          matchIndex++;\n        });\n\n        isValueEqual = hasMatch;\n      }\n    });\n  }\n\n  return isValueEqual;\n}\n", "import {\n  areArraysEqual,\n  areMapsEqual,\n  areObjectsEqual,\n  areRegExpsEqual,\n  areSetsEqual,\n  isPlainObject,\n  isPromiseLike,\n  sameValueZeroEqual,\n} from './utils';\n\nimport type { EqualityComparator, InternalEqualityComparator } from './types';\n\nconst HAS_MAP_SUPPORT = typeof Map === 'function';\nconst HAS_SET_SUPPORT = typeof Set === 'function';\n\nconst { valueOf } = Object.prototype;\n\nexport type EqualityComparatorCreator = (\n  fn: EqualityComparator,\n) => InternalEqualityComparator;\n\nexport function createComparator(\n  createIsEqual?: EqualityComparatorCreator,\n): EqualityComparator {\n  const isEqual: InternalEqualityComparator =\n    /* eslint-disable no-use-before-define */\n    typeof createIsEqual === 'function'\n      ? createIsEqual(comparator)\n      : (\n          a: any,\n          b: any,\n          indexOrKeyA: any,\n          indexOrKeyB: any,\n          parentA: any,\n          parentB: any,\n          meta: any,\n        ) => comparator(a, b, meta);\n  /* eslint-enable */\n\n  /**\n   * compare the value of the two objects and return true if they are equivalent in values\n   *\n   * @param a the value to test against\n   * @param b the value to test\n   * @param [meta] an optional meta object that is passed through to all equality test calls\n   * @returns are a and b equivalent in value\n   */\n  function comparator(a: any, b: any, meta?: any) {\n    if (a === b) {\n      return true;\n    }\n\n    if (a && b && typeof a === 'object' && typeof b === 'object') {\n      if (isPlainObject(a) && isPlainObject(b)) {\n        return areObjectsEqual(a, b, isEqual, meta);\n      }\n\n      let aShape = Array.isArray(a);\n      let bShape = Array.isArray(b);\n\n      if (aShape || bShape) {\n        return aShape === bShape && areArraysEqual(a, b, isEqual, meta);\n      }\n\n      aShape = a instanceof Date;\n      bShape = b instanceof Date;\n\n      if (aShape || bShape) {\n        return (\n          aShape === bShape && sameValueZeroEqual(a.getTime(), b.getTime())\n        );\n      }\n\n      aShape = a instanceof RegExp;\n      bShape = b instanceof RegExp;\n\n      if (aShape || bShape) {\n        return aShape === bShape && areRegExpsEqual(a, b);\n      }\n\n      if (isPromiseLike(a) || isPromiseLike(b)) {\n        return a === b;\n      }\n\n      if (HAS_MAP_SUPPORT) {\n        aShape = a instanceof Map;\n        bShape = b instanceof Map;\n\n        if (aShape || bShape) {\n          return aShape === bShape && areMapsEqual(a, b, isEqual, meta);\n        }\n      }\n\n      if (HAS_SET_SUPPORT) {\n        aShape = a instanceof Set;\n        bShape = b instanceof Set;\n\n        if (aShape || bShape) {\n          return aShape === bShape && areSetsEqual(a, b, isEqual, meta);\n        }\n      }\n\n      if (a.valueOf !== valueOf || b.valueOf !== valueOf) {\n        return sameValueZeroEqual(a.valueOf(), b.valueOf());\n      }\n\n      return areObjectsEqual(a, b, isEqual, meta);\n    }\n\n    return a !== a && b !== b;\n  }\n\n  return comparator;\n}\n", "import { createComparator } from './comparator';\nimport { createCircularEqualCreator, sameValueZeroEqual } from './utils';\n\nexport { createComparator as createCustomEqual, sameValueZeroEqual };\n\nexport const deepEqual = createComparator();\nexport const shallowEqual = createComparator(() => sameValueZeroEqual);\n\nexport const circularDeepEqual = createComparator(createCircularEqualCreator());\nexport const circularShallowEqual = createComparator(\n  createCircularEqualCreator(sameValueZeroEqual),\n);\n"], "names": [], "mappings": "AAQA,IAAM,oBAAoB,GAAG,OAAO,OAAO,KAAK,UAAU,CAAC;AAEnD,IAAA,IAAI,GAAK,MAAM,CAAA,IAAX,CAAY;AAExB;;;;;;AAMG;AACa,SAAA,kBAAkB,CAAC,CAAM,EAAE,CAAM,EAAA;AAC/C,IAAA,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;AACzC,CAAC;AAED;;;;;AAKG;AACG,SAAU,aAAa,CAAC,KAAU,EAAA;IACtC,OAAO,KAAK,CAAC,WAAW,KAAK,MAAM,IAAI,KAAK,CAAC,WAAW,IAAI,IAAI,CAAC;AACnE,CAAC;AAED;;;;;AAKG;AACG,SAAU,aAAa,CAAC,KAAU,EAAA;IACtC,OAAO,CAAC,CAAC,KAAK,IAAI,OAAO,KAAK,CAAC,IAAI,KAAK,UAAU,CAAC;AACrD,CAAC;AAED;;;;;AAKG;AACG,SAAU,cAAc,CAAC,KAAU,EAAA;IACvC,OAAO,CAAC,EAAE,KAAK,IAAI,KAAK,CAAC,QAAQ,CAAC,CAAC;AACrC,CAAC;AAED;;;;;AAKG;SACa,mBAAmB,GAAA;IACjC,IAAM,OAAO,GAAuB,EAAE,CAAC;IAEvC,OAAO;QACL,MAAM,EAAN,UAAO,GAAW,EAAA;AAChB,YAAA,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,OAAO,CAAC,MAAM,EAAE,EAAE,KAAK,EAAE;gBACnD,IAAI,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;AAC7B,oBAAA,OAAO,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;oBACzB,OAAO;AACR,iBAAA;AACF,aAAA;SACF;QAED,GAAG,EAAH,UAAI,GAAW,EAAA;AACb,YAAA,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,OAAO,CAAC,MAAM,EAAE,EAAE,KAAK,EAAE;gBACnD,IAAI,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;AAC7B,oBAAA,OAAO,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;AAC1B,iBAAA;AACF,aAAA;SACF;AAED,QAAA,GAAG,EAAH,UAAI,GAAW,EAAE,KAAa,EAAA;AAC5B,YAAA,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,OAAO,CAAC,MAAM,EAAE,EAAE,KAAK,EAAE;gBACnD,IAAI,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;oBAC7B,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC;oBAC1B,OAAO;AACR,iBAAA;AACF,aAAA;YAED,OAAO,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC,CAAC;SAC5B;KACF,CAAC;AACJ,CAAC;AAED;;;;AAIG;AACI,IAAM,WAAW,GAAG,CAAC,UAAC,aAAsB,EAAA;AACjD,IAAA,IAAI,aAAa,EAAE;AACjB,QAAA,OAAO,SAAS,YAAY,GAAA;YAC1B,OAAO,IAAI,OAAO,EAAE,CAAC;AACvB,SAAC,CAAC;AACH,KAAA;AAED,IAAA,OAAO,mBAAmB,CAAC;AAC7B,CAAC,EAAE,oBAAoB,CAAC,CAAC;AAEzB;;;;;AAKG;AACG,SAAU,0BAA0B,CAAC,OAA4B,EAAA;IACrE,OAAO,SAAS,mBAAmB,CACjC,UAA8B,EAAA;AAE9B,QAAA,IAAM,WAAW,GAAG,OAAO,IAAI,UAAU,CAAC;AAE1C,QAAA,OAAO,SAAS,aAAa,CAC3B,CAAC,EACD,CAAC,EACD,WAAW,EACX,WAAW,EACX,OAAO,EACP,OAAO,EACP,KAA4B,EAAA;YAA5B,IAAA,KAAA,KAAA,KAAA,CAAA,EAAA,EAAA,KAAe,GAAA,WAAW,EAAE,CAAA,EAAA;YAE5B,IAAM,YAAY,GAAG,CAAC,CAAC,CAAC,IAAI,OAAO,CAAC,KAAK,QAAQ,CAAC;YAClD,IAAM,YAAY,GAAG,CAAC,CAAC,CAAC,IAAI,OAAO,CAAC,KAAK,QAAQ,CAAC;YAElD,IAAI,YAAY,KAAK,YAAY,EAAE;AACjC,gBAAA,OAAO,KAAK,CAAC;AACd,aAAA;AAED,YAAA,IAAI,CAAC,YAAY,IAAI,CAAC,YAAY,EAAE;gBAClC,OAAO,WAAW,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC;AACjC,aAAA;YAED,IAAM,OAAO,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;YAE7B,IAAG,OAAO,IAAI,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE;gBAC1B,OAAO,OAAO,KAAK,CAAC,CAAC;AACtB,aAAA;AAED,YAAA,KAAK,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AAChB,YAAA,KAAK,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YAEhB,IAAM,MAAM,GAAG,WAAW,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC;AAExC,YAAA,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;AAChB,YAAA,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;AAEhB,YAAA,OAAO,MAAM,CAAC;AAChB,SAAC,CAAC;AACJ,KAAC,CAAC;AACJ,CAAC;AAED;;;;;;;;AAQG;AACG,SAAU,cAAc,CAC5B,CAAQ,EACR,CAAQ,EACR,OAAmC,EACnC,IAAS,EAAA;AAET,IAAA,IAAI,KAAK,GAAG,CAAC,CAAC,MAAM,CAAC;AAErB,IAAA,IAAI,CAAC,CAAC,MAAM,KAAK,KAAK,EAAE;AACtB,QAAA,OAAO,KAAK,CAAC;AACd,KAAA;AAED,IAAA,OAAO,KAAK,EAAE,GAAG,CAAC,EAAE;QAClB,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,EAAE;AAC1D,YAAA,OAAO,KAAK,CAAC;AACd,SAAA;AACF,KAAA;AAED,IAAA,OAAO,IAAI,CAAC;AACd,CAAC;AAED;;;;;;;;AAQG;AACG,SAAU,YAAY,CAC1B,CAAgB,EAChB,CAAgB,EAChB,OAAmC,EACnC,IAAS,EAAA;IAET,IAAI,YAAY,GAAG,CAAC,CAAC,IAAI,KAAK,CAAC,CAAC,IAAI,CAAC;AAErC,IAAA,IAAI,YAAY,IAAI,CAAC,CAAC,IAAI,EAAE;QAC1B,IAAM,gBAAc,GAAyB,EAAE,CAAC;QAChD,IAAI,QAAM,GAAG,CAAC,CAAC;AAEf,QAAA,CAAC,CAAC,OAAO,CAAC,UAAC,MAAM,EAAE,IAAI,EAAA;AACrB,YAAA,IAAI,YAAY,EAAE;gBAChB,IAAI,UAAQ,GAAG,KAAK,CAAC;gBACrB,IAAI,aAAW,GAAG,CAAC,CAAC;AAEpB,gBAAA,CAAC,CAAC,OAAO,CAAC,UAAC,MAAM,EAAE,IAAI,EAAA;oBACrB,IAAI,CAAC,UAAQ,IAAI,CAAC,gBAAc,CAAC,aAAW,CAAC,EAAE;wBAC7C,UAAQ;AACN,4BAAA,OAAO,CAAC,IAAI,EAAE,IAAI,EAAE,QAAM,EAAE,aAAW,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC;AACpD,gCAAA,OAAO,CAAC,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC;AAElD,wBAAA,IAAI,UAAQ,EAAE;AACZ,4BAAA,gBAAc,CAAC,aAAW,CAAC,GAAG,IAAI,CAAC;AACpC,yBAAA;AACF,qBAAA;AAED,oBAAA,aAAW,EAAE,CAAC;AAChB,iBAAC,CAAC,CAAC;AAEH,gBAAA,QAAM,EAAE,CAAC;gBACT,YAAY,GAAG,UAAQ,CAAC;AACzB,aAAA;AACH,SAAC,CAAC,CAAC;AACJ,KAAA;AAED,IAAA,OAAO,YAAY,CAAC;AACtB,CAAC;AAOD,IAAM,KAAK,GAAG,QAAQ,CAAC;AAEvB,IAAM,cAAc,GAAG,QAAQ,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CACjD,QAAQ,CAAC,SAAS,CAAC,IAAI,EACvB,MAAM,CAAC,SAAS,CAAC,cAAc,CAChC,CAAC;AAEF;;;;;;;;AAQG;AACG,SAAU,eAAe,CAC7B,CAAkB,EAClB,CAAkB,EAClB,OAAmC,EACnC,IAAS,EAAA;AAET,IAAA,IAAM,KAAK,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;AAEtB,IAAA,IAAI,KAAK,GAAG,KAAK,CAAC,MAAM,CAAC;IAEzB,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM,KAAK,KAAK,EAAE;AAC5B,QAAA,OAAO,KAAK,CAAC;AACd,KAAA;AAED,IAAA,IAAI,KAAK,EAAE;QACT,IAAI,GAAG,SAAQ,CAAC;AAEhB,QAAA,OAAO,KAAK,EAAE,GAAG,CAAC,EAAE;AAClB,YAAA,GAAG,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC;YAEnB,IAAI,GAAG,KAAK,KAAK,EAAE;AACjB,gBAAA,IAAM,aAAa,GAAG,cAAc,CAAC,CAAC,CAAC,CAAC;AACxC,gBAAA,IAAM,aAAa,GAAG,cAAc,CAAC,CAAC,CAAC,CAAC;AAExC,gBAAA,IACE,CAAC,aAAa,IAAI,aAAa;oBAC/B,aAAa,KAAK,aAAa,EAC/B;AACA,oBAAA,OAAO,KAAK,CAAC;AACd,iBAAA;AACF,aAAA;AAED,YAAA,IACE,CAAC,cAAc,CAAC,CAAC,EAAE,GAAG,CAAC;gBACvB,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,EAC9C;AACA,gBAAA,OAAO,KAAK,CAAC;AACd,aAAA;AACF,SAAA;AACF,KAAA;AAED,IAAA,OAAO,IAAI,CAAC;AACd,CAAC;AAED;;;;;;AAMG;AACI,IAAM,eAAe,GAAG,CAAC,YAAA;AAC9B,IAAA,IAAI,MAAM,CAAC,KAAK,KAAK,GAAG,EAAE;AACxB,QAAA,OAAO,SAAS,eAAe,CAAC,CAAS,EAAE,CAAS,EAAA;AAClD,YAAA,OAAO,CAAC,CAAC,MAAM,KAAK,CAAC,CAAC,MAAM,IAAI,CAAC,CAAC,KAAK,KAAK,CAAC,CAAC,KAAK,CAAC;AACtD,SAAC,CAAC;AACH,KAAA;AAED,IAAA,OAAO,SAAS,uBAAuB,CAAC,CAAS,EAAE,CAAS,EAAA;AAC1D,QAAA,QACE,CAAC,CAAC,MAAM,KAAK,CAAC,CAAC,MAAM;AACrB,YAAA,CAAC,CAAC,MAAM,KAAK,CAAC,CAAC,MAAM;AACrB,YAAA,CAAC,CAAC,UAAU,KAAK,CAAC,CAAC,UAAU;AAC7B,YAAA,CAAC,CAAC,SAAS,KAAK,CAAC,CAAC,SAAS;AAC3B,YAAA,CAAC,CAAC,OAAO,KAAK,CAAC,CAAC,OAAO;AACvB,YAAA,CAAC,CAAC,MAAM,KAAK,CAAC,CAAC,MAAM;AACrB,YAAA,CAAC,CAAC,SAAS,KAAK,CAAC,CAAC,SAAS,EAC3B;AACJ,KAAC,CAAC;AACJ,CAAC,GAAG,CAAC;AAEL;;;;;;;;AAQG;AACG,SAAU,YAAY,CAC1B,CAAW,EACX,CAAW,EACX,OAAmC,EACnC,IAAS,EAAA;IAET,IAAI,YAAY,GAAG,CAAC,CAAC,IAAI,KAAK,CAAC,CAAC,IAAI,CAAC;AAErC,IAAA,IAAI,YAAY,IAAI,CAAC,CAAC,IAAI,EAAE;QAC1B,IAAM,gBAAc,GAAyB,EAAE,CAAC;AAEhD,QAAA,CAAC,CAAC,OAAO,CAAC,UAAC,MAAM,EAAE,IAAI,EAAA;AACrB,YAAA,IAAI,YAAY,EAAE;gBAChB,IAAI,UAAQ,GAAG,KAAK,CAAC;gBACrB,IAAI,YAAU,GAAG,CAAC,CAAC;AAEnB,gBAAA,CAAC,CAAC,OAAO,CAAC,UAAC,MAAM,EAAE,IAAI,EAAA;oBACrB,IAAI,CAAC,UAAQ,IAAI,CAAC,gBAAc,CAAC,YAAU,CAAC,EAAE;AAC5C,wBAAA,UAAQ,GAAG,OAAO,CAAC,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC;AAE3D,wBAAA,IAAI,UAAQ,EAAE;AACZ,4BAAA,gBAAc,CAAC,YAAU,CAAC,GAAG,IAAI,CAAC;AACnC,yBAAA;AACF,qBAAA;AAED,oBAAA,YAAU,EAAE,CAAC;AACf,iBAAC,CAAC,CAAC;gBAEH,YAAY,GAAG,UAAQ,CAAC;AACzB,aAAA;AACH,SAAC,CAAC,CAAC;AACJ,KAAA;AAED,IAAA,OAAO,YAAY,CAAC;AACtB;;ACxWA,IAAM,eAAe,GAAG,OAAO,GAAG,KAAK,UAAU,CAAC;AAClD,IAAM,eAAe,GAAG,OAAO,GAAG,KAAK,UAAU,CAAC;AAE1C,IAAA,OAAO,GAAK,MAAM,CAAC,SAAS,QAArB,CAAsB;AAM/B,SAAU,gBAAgB,CAC9B,aAAyC,EAAA;AAEzC,IAAA,IAAM,OAAO;;IAEX,OAAO,aAAa,KAAK,UAAU;AACjC,UAAE,aAAa,CAAC,UAAU,CAAC;UACzB,UACE,CAAM,EACN,CAAM,EACN,WAAgB,EAChB,WAAgB,EAChB,OAAY,EACZ,OAAY,EACZ,IAAS,EACN,EAAA,OAAA,UAAU,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,CAAtB,EAAsB,CAAC;;AAGlC;;;;;;;AAOG;AACH,IAAA,SAAS,UAAU,CAAC,CAAM,EAAE,CAAM,EAAE,IAAU,EAAA;QAC5C,IAAI,CAAC,KAAK,CAAC,EAAE;AACX,YAAA,OAAO,IAAI,CAAC;AACb,SAAA;AAED,QAAA,IAAI,CAAC,IAAI,CAAC,IAAI,OAAO,CAAC,KAAK,QAAQ,IAAI,OAAO,CAAC,KAAK,QAAQ,EAAE;YAC5D,IAAI,aAAa,CAAC,CAAC,CAAC,IAAI,aAAa,CAAC,CAAC,CAAC,EAAE;gBACxC,OAAO,eAAe,CAAC,CAAC,EAAE,CAAC,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC;AAC7C,aAAA;YAED,IAAI,MAAM,GAAG,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;YAC9B,IAAI,MAAM,GAAG,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;YAE9B,IAAI,MAAM,IAAI,MAAM,EAAE;AACpB,gBAAA,OAAO,MAAM,KAAK,MAAM,IAAI,cAAc,CAAC,CAAC,EAAE,CAAC,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC;AACjE,aAAA;AAED,YAAA,MAAM,GAAG,CAAC,YAAY,IAAI,CAAC;AAC3B,YAAA,MAAM,GAAG,CAAC,YAAY,IAAI,CAAC;YAE3B,IAAI,MAAM,IAAI,MAAM,EAAE;AACpB,gBAAA,QACE,MAAM,KAAK,MAAM,IAAI,kBAAkB,CAAC,CAAC,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC,EACjE;AACH,aAAA;AAED,YAAA,MAAM,GAAG,CAAC,YAAY,MAAM,CAAC;AAC7B,YAAA,MAAM,GAAG,CAAC,YAAY,MAAM,CAAC;YAE7B,IAAI,MAAM,IAAI,MAAM,EAAE;gBACpB,OAAO,MAAM,KAAK,MAAM,IAAI,eAAe,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AACnD,aAAA;YAED,IAAI,aAAa,CAAC,CAAC,CAAC,IAAI,aAAa,CAAC,CAAC,CAAC,EAAE;gBACxC,OAAO,CAAC,KAAK,CAAC,CAAC;AAChB,aAAA;AAED,YAAA,IAAI,eAAe,EAAE;AACnB,gBAAA,MAAM,GAAG,CAAC,YAAY,GAAG,CAAC;AAC1B,gBAAA,MAAM,GAAG,CAAC,YAAY,GAAG,CAAC;gBAE1B,IAAI,MAAM,IAAI,MAAM,EAAE;AACpB,oBAAA,OAAO,MAAM,KAAK,MAAM,IAAI,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC;AAC/D,iBAAA;AACF,aAAA;AAED,YAAA,IAAI,eAAe,EAAE;AACnB,gBAAA,MAAM,GAAG,CAAC,YAAY,GAAG,CAAC;AAC1B,gBAAA,MAAM,GAAG,CAAC,YAAY,GAAG,CAAC;gBAE1B,IAAI,MAAM,IAAI,MAAM,EAAE;AACpB,oBAAA,OAAO,MAAM,KAAK,MAAM,IAAI,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC;AAC/D,iBAAA;AACF,aAAA;YAED,IAAI,CAAC,CAAC,OAAO,KAAK,OAAO,IAAI,CAAC,CAAC,OAAO,KAAK,OAAO,EAAE;AAClD,gBAAA,OAAO,kBAAkB,CAAC,CAAC,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC;AACrD,aAAA;YAED,OAAO,eAAe,CAAC,CAAC,EAAE,CAAC,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC;AAC7C,SAAA;AAED,QAAA,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;KAC3B;AAED,IAAA,OAAO,UAAU,CAAC;AACpB;;AC7Ga,IAAA,SAAS,GAAG,gBAAgB,GAAG;AACrC,IAAM,YAAY,GAAG,gBAAgB,CAAC,YAAM,EAAA,OAAA,kBAAkB,CAAA,EAAA,EAAE;IAE1D,iBAAiB,GAAG,gBAAgB,CAAC,0BAA0B,EAAE,EAAE;AACnE,IAAA,oBAAoB,GAAG,gBAAgB,CAClD,0BAA0B,CAAC,kBAAkB,CAAC;;;;"}