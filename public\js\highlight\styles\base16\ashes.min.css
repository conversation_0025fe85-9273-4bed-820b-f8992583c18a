/*!
  Theme: Ashes
  Author: <PERSON><PERSON> (https://github.com/janniks)
  License: ~ MIT (or more permissive) [via base16-schemes-source]
  Maintainer: @highlightjs/core-team
  Version: 2021.09.0
*/pre code.hljs{display:block;overflow-x:auto;padding:1em}code.hljs{padding:3px 5px}.hljs{color:#c7ccd1;background:#1c2023}.hljs ::selection,.hljs::selection{background-color:#565e65;color:#c7ccd1}.hljs-comment{color:#747c84}.hljs-tag{color:#adb3ba}.hljs-operator,.hljs-punctuation,.hljs-subst{color:#c7ccd1}.hljs-operator{opacity:.7}.hljs-bullet,.hljs-deletion,.hljs-name,.hljs-selector-tag,.hljs-template-variable,.hljs-variable{color:#c7ae95}.hljs-attr,.hljs-link,.hljs-literal,.hljs-number,.hljs-symbol,.hljs-variable.constant_{color:#c7c795}.hljs-class .hljs-title,.hljs-title,.hljs-title.class_{color:#aec795}.hljs-strong{font-weight:700;color:#aec795}.hljs-addition,.hljs-code,.hljs-string,.hljs-title.class_.inherited__{color:#95c7ae}.hljs-built_in,.hljs-doctag,.hljs-keyword.hljs-atrule,.hljs-quote,.hljs-regexp{color:#95aec7}.hljs-attribute,.hljs-function .hljs-title,.hljs-section,.hljs-title.function_,.ruby .hljs-property{color:#ae95c7}.diff .hljs-meta,.hljs-keyword,.hljs-template-tag,.hljs-type{color:#c795ae}.hljs-emphasis{color:#c795ae;font-style:italic}.hljs-meta,.hljs-meta .hljs-keyword,.hljs-meta .hljs-string{color:#c79595}.hljs-meta .hljs-keyword,.hljs-meta-keyword{font-weight:700}