{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../lib/extend/index.ts"], "names": [], "mappings": ";;;;;;AAAA,qCAA+C;AAAtC,mHAAA,OAAO,OAAW;AAC3B,uCAAiD;AAAxC,qHAAA,OAAO,OAAY;AAC5B,mCAA6C;AAApC,iHAAA,OAAO,OAAU;AAC1B,yCAAmD;AAA1C,uHAAA,OAAO,OAAa;AAC7B,mCAA6C;AAApC,iHAAA,OAAO,OAAU;AAC1B,uDAA0D;AAAjD,8HAAA,OAAO,OAAa;AAC7B,uCAAiD;AAAxC,qHAAA,OAAO,OAAY;AAC5B,uCAAiD;AAAxC,qHAAA,OAAO,OAAY;AAC5B,yCAAmD;AAA1C,uHAAA,OAAO,OAAa;AAC7B,uCAAiD;AAAxC,qHAAA,OAAO,OAAY;AAC5B,6BAAuC;AAA9B,2GAAA,OAAO,OAAO"}