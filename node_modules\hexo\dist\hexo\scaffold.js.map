{"version": 3, "file": "scaffold.js", "sourceRoot": "", "sources": ["../../lib/hexo/scaffold.ts"], "names": [], "mappings": ";AAAA,+BAAqC;AACrC,qCAAuE;AAKvE,MAAM,QAAQ;IAOZ,YAAY,OAAa;QACvB,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACvB,IAAI,CAAC,WAAW,GAAG,OAAO,CAAC,YAAY,CAAC;QACxC,IAAI,CAAC,QAAQ,GAAG;YACd,MAAM,EAAE;gBACN,KAAK;gBACL,sBAAsB;gBACtB,oBAAoB;gBACpB,kBAAkB;gBAClB,OAAO;gBACP,KAAK;aACN,CAAC,IAAI,CAAC,IAAI,CAAC;SACb,CAAC;IACJ,CAAC;IAED,QAAQ;QAIN,MAAM,EAAE,WAAW,EAAE,GAAG,IAAI,CAAC;QAE7B,OAAO,IAAA,gBAAM,EAAC,WAAW,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;YACtC,IAAI,CAAC,KAAK;gBAAE,OAAO,EAAE,CAAC;YAEtB,OAAO,IAAA,iBAAO,EAAC,WAAW,EAAE;gBAC1B,aAAa,EAAE,QAAQ;aACxB,CAAC,CAAC;QACL,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YACd,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM,GAAG,IAAA,cAAO,EAAC,IAAI,CAAC,CAAC,MAAM,CAAC;YAC3D,IAAI,EAAE,IAAA,WAAI,EAAC,WAAW,EAAE,IAAI,CAAC;SAC9B,CAAC,CAAC,CAAC;IACN,CAAC;IAED,YAAY,CAAC,IAAY;QAIvB,OAAO,IAAI,CAAC,QAAQ,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC,CAAC,CAAC;IAC7E,CAAC;IAED,GAAG,CAAC,IAAY,EAAE,QAAkC;QAClD,OAAO,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;YACzC,IAAI,IAAI,EAAE,CAAC;gBACT,OAAO,IAAA,kBAAQ,EAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC7B,CAAC;YAED,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;QAC7B,CAAC,CAAC,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;IAC1B,CAAC;IAED,GAAG,CAAC,IAAY,EAAE,OAAY,EAAE,QAAmC;QACjE,MAAM,EAAE,WAAW,EAAE,GAAG,IAAI,CAAC;QAE7B,OAAO,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;YACzC,IAAI,IAAI,GAAG,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,IAAA,WAAI,EAAC,WAAW,EAAE,IAAI,CAAC,CAAC;YACtD,IAAI,CAAC,IAAA,cAAO,EAAC,IAAI,CAAC;gBAAE,IAAI,IAAI,KAAK,CAAC;YAElC,OAAO,IAAA,mBAAS,EAAC,IAAI,EAAE,OAAO,CAAC,CAAC;QAClC,CAAC,CAAC,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;IAC1B,CAAC;IAED,MAAM,CAAC,IAAY,EAAE,QAAmC;QACtD,OAAO,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;YACzC,IAAI,CAAC,IAAI;gBAAE,OAAO;YAElB,OAAO,IAAA,gBAAM,EAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC3B,CAAC,CAAC,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;IAC1B,CAAC;CACF;AAED,iBAAS,QAAQ,CAAC"}