<h1 id="Highlight-js-CDN-Assets"><a href="#Highlight-js-CDN-Assets" class="headerlink" title="Highlight.js CDN Assets"></a>Highlight.js CDN Assets</h1><p><a href="https://packagephobia.now.sh/result?p=highlight.js"><img src="https://packagephobia.now.sh/badge?p=highlight.js" alt="install size"></a></p>
<p><strong>This package contains only the CDN build assets of highlight.js.</strong></p>
<p>This may be what you want if you’d like to install the pre-built distributable highlight.js client-side assets via NPM. If you’re wanting to use highlight.js mainly on the server-side you likely want the <a href="https://www.npmjs.com/package/highlight.js">highlight.js</a> package instead.</p>
<p>To access these files via CDN:<br><br><a href="https://cdn.jsdelivr.net/gh/highlightjs/cdn-release@latest/build/">https://cdn.jsdelivr.net/gh/highlightjs/cdn-release@latest/build/</a></p>
<p><strong>If you just want a single .js file with the common languages built-in:<br><a href="https://cdn.jsdelivr.net/gh/highlightjs/cdn-release@latest/build/highlight.min.js">https://cdn.jsdelivr.net/gh/highlightjs/cdn-release@latest/build/highlight.min.js</a></strong></p>
<hr>
<h2 id="Highlight-js"><a href="#Highlight-js" class="headerlink" title="Highlight.js"></a>Highlight.js</h2><p>Highlight.js is a syntax highlighter written in JavaScript. It works in<br>the browser as well as on the server. It works with pretty much any<br>markup, doesn’t depend on any framework, and has automatic language<br>detection.</p>
<p>If you’d like to read the full README:<br><br><a href="https://github.com/highlightjs/highlight.js/blob/main/README.md">https://github.com/highlightjs/highlight.js/blob/main/README.md</a></p>
<h2 id="License"><a href="#License" class="headerlink" title="License"></a>License</h2><p>Highlight.js is released under the BSD License. See <a href="https://github.com/highlightjs/highlight.js/blob/main/LICENSE">LICENSE</a> file<br>for details.</p>
<h2 id="Links"><a href="#Links" class="headerlink" title="Links"></a>Links</h2><p>The official site for the library is at <a href="https://highlightjs.org/">https://highlightjs.org/</a>.</p>
<p>The Github project may be found at: <a href="https://github.com/highlightjs/highlight.js">https://github.com/highlightjs/highlight.js</a></p>
<p>Further in-depth documentation for the API and other topics is at<br><a href="http://highlightjs.readthedocs.io/">http://highlightjs.readthedocs.io/</a>.</p>
<p>A list of the Core Team and contributors can be found in the <a href="https://github.com/highlightjs/highlight.js/blob/main/CONTRIBUTORS.md">CONTRIBUTORS.md</a> file.</p>
