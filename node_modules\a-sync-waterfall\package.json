{"name": "a-sync-waterfall", "version": "1.0.1", "description": "Runs a list of async tasks, passing the results of each into the next one", "author": {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/hydiak/a-sync-waterfall"}, "license": "MIT", "homepage": "https://github.com/hydiak/a-sync-waterfall", "repository": {"type": "git", "url": "**************:hydiak/a-sync-waterfall.git"}, "bugs": {"url": "https://github.com/hydiak/a-sync-waterfall/issues"}, "main": "./index", "keywords": ["async", "sync", "waterfall", "tasks", "control", "flow"], "dependencies": {}}