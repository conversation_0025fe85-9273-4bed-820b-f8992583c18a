{"version": 3, "file": "console.js", "sourceRoot": "", "sources": ["../../lib/extend/console.ts"], "names": [], "mappings": ";;;;AAAA,wDAA+B;AAC/B,oDAA4B;AAkC5B,MAAM,OAAO;IAIX;QACE,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC;QAChB,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC;IAClB,CAAC;IAED;;;;OAIG;IACH,GAAG,CAAC,IAAY;QACd,IAAI,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;QAC1B,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC;IACtC,CAAC;IAED,IAAI;QACF,OAAO,IAAI,CAAC,KAAK,CAAC;IACpB,CAAC;IAaD,QAAQ,CAAC,IAAY,EAAE,IAA6B,EAAE,OAAwB,EAAE,EAAU;QACxF,IAAI,CAAC,IAAI;YAAE,MAAM,IAAI,SAAS,CAAC,kBAAkB,CAAC,CAAC;QAEnD,IAAI,CAAC,EAAE,EAAE,CAAC;YACR,IAAI,OAAO,EAAE,CAAC;gBACZ,IAAI,OAAO,OAAO,KAAK,UAAU,EAAE,CAAC;oBAClC,EAAE,GAAG,OAAO,CAAC;oBAEb,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE,CAAC,CAAC,oBAAoB;wBAClD,OAAO,GAAG,IAAI,CAAC;wBACf,IAAI,GAAG,EAAE,CAAC;oBACZ,CAAC;yBAAM,CAAC,CAAC,iBAAiB;wBACxB,OAAO,GAAG,EAAE,CAAC;oBACf,CAAC;gBACH,CAAC;qBAAM,CAAC;oBACN,MAAM,IAAI,SAAS,CAAC,uBAAuB,CAAC,CAAC;gBAC/C,CAAC;YACH,CAAC;iBAAM,CAAC;gBACN,WAAW;gBACX,IAAI,OAAO,IAAI,KAAK,UAAU,EAAE,CAAC;oBAC/B,EAAE,GAAG,IAAI,CAAC;oBACV,OAAO,GAAG,EAAE,CAAC;oBACb,IAAI,GAAG,EAAE,CAAC;gBACZ,CAAC;qBAAM,CAAC;oBACN,MAAM,IAAI,SAAS,CAAC,uBAAuB,CAAC,CAAC;gBAC/C,CAAC;YACH,CAAC;QACH,CAAC;QAED,IAAI,EAAE,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAClB,EAAE,GAAG,kBAAO,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC;QAC7B,CAAC;aAAM,CAAC;YACN,EAAE,GAAG,kBAAO,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;QAC1B,CAAC;QAED,MAAM,CAAC,GAAG,EAAmB,CAAC;QAC9B,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,GAAG,CAAC,CAAC;QACnC,CAAC,CAAC,OAAO,GAAG,OAAiB,CAAC;QAC9B,CAAC,CAAC,IAAI,GAAG,IAAc,CAAC;QAExB,IAAI,CAAC,KAAK,GAAG,IAAA,gBAAM,EAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;IAC/C,CAAC;CACF;AAED,iBAAS,OAAO,CAAC"}