.index {
  display: flex;
  padding-bottom: 15px;
}
.index-right {
  flex: 1;
}
.index-left,
.index-center {
  flex: 2;
}
.index-center {
  margin: 0 8px;
}
.index-right {
  position: relative;
}
.index-right-wrapper {
  position: fixed;
  width: 140px;
}
.index-right-wrapper > ul {
  width: 100%;
  margin-top: 15px;
  padding: 7px;
  background-color: #fff;
  border-radius: 5px;
  box-shadow: 0 0 5px #333;
}
.index-right-wrapper .category-list,
.index-right-wrapper .tags,
.index-right-wrapper .archive-list {
  max-height: 300px;
  overflow-y: auto !important;
}
.index-right-wrapper .category-list {
  margin-top: 0;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  overflow: hidden;
}
.index-right-wrapper .category-list > li {
  width: 100%;
  text-align: center;
  padding: 8px;
}
.index-right-wrapper .category-list > li:hover {
  border-radius: 5px;
  background: #ffa500;
}
.index-right-wrapper .category-list > li:hover a {
  color: #fff;
  display: block;
}
.index-right-wrapper .category-list > li:last-child {
  margin-bottom: 0;
}
.index-right-wrapper .tags {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  padding: 10px;
}
.index-right-wrapper .tags li {
  padding: 5px;
}
.index-right-wrapper .tags li a {
  color: #fff;
  font-size: 12px;
  padding: 3px 7px;
  border-radius: 5px;
  background-color: #ff6347;
}
.index-right-wrapper .tags li a:hover {
  text-decoration: underline;
}
.index-right-wrapper .archive-list {
  padding: 10px;
  display: flex;
  flex-direction: column;
}
.index-right-wrapper .archive-list > li {
  display: flex;
  justify-content: space-between;
  padding: 3px 0;
}
.index-right-wrapper .archive-list > li:hover > a:first-child {
  transform: translateX(5px);
  text-decoration: underline;
}
.index-right-wrapper .archive-list > li > span,
.index-right-wrapper .archive-list > li > a {
  color: #333;
  font-size: 13px;
  transition: all 0.3s;
}
.index-right-wrapper .backtop {
  padding: 10px;
  display: inline-flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  margin-top: 15px;
  color: #333;
  position: relative;
  cursor: pointer;
  overflow: hidden;
  background-color: #fff;
  border-radius: 5px;
  box-shadow: 0 0 5px #333;
}
.index-right-wrapper .backtop::after {
  content: '';
  position: absolute;
  width: 100%;
  height: 100%;
  background-color: #87ceeb;
  transition: all 0.3s;
  border-radius: 50%;
  transform: scale(0);
}
.index-right-wrapper .backtop:hover {
  color: #fff;
}
.index-right-wrapper .backtop:hover::after {
  transform: scale(1);
  border-radius: 0;
}
.index-right-wrapper .backtop i {
  font-size: 24px;
  z-index: 2;
  transition: all 0.3s;
}
.index-right-wrapper .backtop span {
  margin-top: 5px;
  font-size: 13px;
  z-index: 2;
  transition: all 0.3s;
}
.index-center .index-article:nth-child(3n) {
  height: 300px;
}
.index-center .index-article:nth-child(3n + 1) {
  height: 270px;
}
.index-center .index-article:nth-child(3n + 2) {
  height: 230px;
}
.index-left .index-article:nth-child(3n) {
  height: 260px;
}
.index-left .index-article:nth-child(3n + 1) {
  height: 220px;
}
.index-left .index-article:nth-child(3n + 2) {
  height: 300px;
}
.index-article {
  width: 100%;
  box-shadow: 0 0 5px #333;
  border-radius: 5px;
  position: relative;
  overflow: hidden;
  margin-top: 10px;
}
.index-article:hover .index-article--cover {
  transform: scale(1.2);
}
.index-article:hover .index-article--content {
  height: 100%;
  background-color: rgba(0,0,0,0.3);
  backdrop-filter: blur(3px);
}
.index-article:hover .index-article--content > div {
  height: auto;
  opacity: 1;
  transform: translateY(15px);
}
.index-article:first-child {
  margin-top: 0;
}
.index-article--box {
  width: 100%;
  height: 100%;
  position: absolute;
  background-color: #ddd;
}
.index-article--box.img-error img {
  object-fit: none;
}
.index-article--box.img-loading::before {
  content: '';
  width: 200%;
  height: 15px;
  background-color: rgba(255,255,255,0.7);
  box-shadow: 0 0 5px #fff;
  transform: rotate(-60deg);
  position: absolute;
  left: -50%;
  top: -100%;
  animation: img-loading 1s infinite;
}
.index-article--box.img-loading::after {
  content: '图片加载中';
  position: absolute;
  left: 50%;
  top: 50%;
  color: #fff;
  font-size: 20px;
  font-weight: bold;
  transform: translate(-50%, -100%);
  animation: img-text-loading 2s infinite linear;
}
.index-article--cover {
  width: 100%;
  height: 100%;
  object-fit: cover;
  position: absolute;
  transition: all 0.3s;
}
.index-article--content {
  position: absolute;
  width: 100%;
  height: 60px;
  bottom: 0;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 2;
  transition: all 0.3s;
  background-color: rgba(0,0,0,0.2);
}
.index-article--content > a {
  color: #fff;
  font-size: 14px;
  font-weight: bold;
  margin: 0 30px;
  text-align: center;
}
.index-article--content > a:hover {
  text-decoration: underline;
}
.index-article--content > div {
  height: 0;
  opacity: 0;
  transform: translateY(50px);
  transition: all 0.5s;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.index-article--content > div a {
  color: #fff;
}
.index-article--content > div a:hover {
  text-decoration: underline;
}
.index-article--content > div > p:first-child {
  padding: 3px 7px;
  border: 1px solid #fff;
}
.index-article--content > div > p:first-child:hover {
  color: #eee;
  background-color: rgba(255,255,255,0.3);
}
.index-article--content > div > p:last-child {
  color: #fff;
  margin-top: 5px;
}
@-moz-keyframes img-text-loading {
  0% {
    content: '图片加载中';
  }
  33% {
    content: '图片加载中.';
  }
  66% {
    content: '图片加载中..';
  }
  100% {
    content: '图片加载中...';
  }
}
@-webkit-keyframes img-text-loading {
  0% {
    content: '图片加载中';
  }
  33% {
    content: '图片加载中.';
  }
  66% {
    content: '图片加载中..';
  }
  100% {
    content: '图片加载中...';
  }
}
@-o-keyframes img-text-loading {
  0% {
    content: '图片加载中';
  }
  33% {
    content: '图片加载中.';
  }
  66% {
    content: '图片加载中..';
  }
  100% {
    content: '图片加载中...';
  }
}
@keyframes img-text-loading {
  0% {
    content: '图片加载中';
  }
  33% {
    content: '图片加载中.';
  }
  66% {
    content: '图片加载中..';
  }
  100% {
    content: '图片加载中...';
  }
}
@-moz-keyframes img-loading {
  0% {
    top: -100%;
  }
  100% {
    top: 200%;
  }
}
@-webkit-keyframes img-loading {
  0% {
    top: -100%;
  }
  100% {
    top: 200%;
  }
}
@-o-keyframes img-loading {
  0% {
    top: -100%;
  }
  100% {
    top: 200%;
  }
}
@keyframes img-loading {
  0% {
    top: -100%;
  }
  100% {
    top: 200%;
  }
}
.page {
  background-color: rgba(0,0,0,0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  height: 50px;
  border-radius: 5px;
  margin-bottom: 30px;
}
.page-number {
  color: #1e90ff;
  padding: 5px 10px;
  border-radius: 3px;
}
.page-number.current {
  color: #fff;
  background-color: #1e90ff;
}
.page .extend {
  color: #1e90ff;
  font-weight: bold;
}
