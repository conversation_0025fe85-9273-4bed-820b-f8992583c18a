/*!
  Theme: Porple
  Author: <PERSON><PERSON> (https://github.com/AuditeMarlow)
  License: ~ MIT (or more permissive) [via base16-schemes-source]
  Maintainer: @highlightjs/core-team
  Version: 2021.09.0
*/pre code.hljs{display:block;overflow-x:auto;padding:1em}code.hljs{padding:3px 5px}.hljs{color:#d8d8d8;background:#292c36}.hljs ::selection,.hljs::selection{background-color:#474160;color:#d8d8d8}.hljs-comment{color:#65568a}.hljs-tag{color:#b8b8b8}.hljs-operator,.hljs-punctuation,.hljs-subst{color:#d8d8d8}.hljs-operator{opacity:.7}.hljs-bullet,.hljs-deletion,.hljs-name,.hljs-selector-tag,.hljs-template-variable,.hljs-variable{color:#f84547}.hlj<PERSON>-attr,.hljs-link,.hljs-literal,.hljs-number,.hljs-symbol,.hljs-variable.constant_{color:#d28e5d}.hljs-class .hljs-title,.hljs-title,.hljs-title.class_{color:#efa16b}.hljs-strong{font-weight:700;color:#efa16b}.hljs-addition,.hljs-code,.hljs-string,.hljs-title.class_.inherited__{color:#95c76f}.hljs-built_in,.hljs-doctag,.hljs-keyword.hljs-atrule,.hljs-quote,.hljs-regexp{color:#64878f}.hljs-attribute,.hljs-function .hljs-title,.hljs-section,.hljs-title.function_,.ruby .hljs-property{color:#8485ce}.diff .hljs-meta,.hljs-keyword,.hljs-template-tag,.hljs-type{color:#b74989}.hljs-emphasis{color:#b74989;font-style:italic}.hljs-meta,.hljs-meta .hljs-keyword,.hljs-meta .hljs-string{color:#986841}.hljs-meta .hljs-keyword,.hljs-meta-keyword{font-weight:700}