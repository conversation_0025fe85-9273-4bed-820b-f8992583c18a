{"version": 3, "file": "injector.js", "sourceRoot": "", "sources": ["../../lib/extend/injector.ts"], "names": [], "mappings": ";AAAA,yCAAkC;AAUlC,MAAM,QAAQ;IAKZ;QACE,IAAI,CAAC,KAAK,GAAG;YACX,UAAU,EAAE,EAAE;YACd,QAAQ,EAAE,EAAE;YACZ,UAAU,EAAE,EAAE;YACd,QAAQ,EAAE,EAAE;SACb,CAAC;QAEF,IAAI,CAAC,KAAK,GAAG,IAAI,iBAAK,EAAE,CAAC;IAC3B,CAAC;IAED,IAAI;QACF,OAAO,IAAI,CAAC,KAAK,CAAC;IACpB,CAAC;IAED,GAAG,CAAC,KAAY,EAAE,EAAE,GAAG,SAAS;QAC9B,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,IAAI,EAAE,CAAC,CAAC;IACjD,CAAC;IAED,OAAO,CAAC,KAAY,EAAE,EAAE,GAAG,SAAS;QAClC,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;QAChC,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM;YAAE,OAAO,EAAE,CAAC;QACnC,OAAO,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;IACtB,CAAC;IAED,OAAO,CAAC,KAAY;QAClB,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,KAAK,OAAO,EAAE,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC;IAClF,CAAC;IAED,QAAQ,CAAC,KAAY,EAAE,KAA8B,EAAE,EAAE,GAAG,SAAS;QACnE,IAAI,CAAC,KAAK;YAAE,MAAM,IAAI,SAAS,CAAC,mBAAmB,CAAC,CAAC;QACrD,IAAI,OAAO,KAAK,KAAK,UAAU;YAAE,KAAK,GAAG,KAAK,EAAE,CAAC;QAEjD,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC;QAC1D,MAAM,QAAQ,GAAG,QAAQ,CAAC,EAAE,CAAC,IAAI,IAAI,GAAG,EAAE,CAAC;QAC3C,QAAQ,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;QACpB,QAAQ,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC;IAC1B,CAAC;IAED,YAAY,CAAC,UAAU;QACrB,IAAI,WAAW,GAAG,SAAS,CAAC;QAC5B,IAAI,UAAU,CAAC,OAAO;YAAE,WAAW,GAAG,MAAM,CAAC;QAC7C,IAAI,UAAU,CAAC,MAAM;YAAE,WAAW,GAAG,MAAM,CAAC;QAC5C,IAAI,UAAU,CAAC,MAAM;YAAE,WAAW,GAAG,MAAM,CAAC;QAC5C,IAAI,UAAU,CAAC,OAAO;YAAE,WAAW,GAAG,SAAS,CAAC;QAChD,IAAI,UAAU,CAAC,QAAQ;YAAE,WAAW,GAAG,UAAU,CAAC;QAClD,IAAI,UAAU,CAAC,GAAG;YAAE,WAAW,GAAG,KAAK,CAAC;QACxC,IAAI,UAAU,CAAC,MAAM;YAAE,WAAW,GAAG,UAAU,CAAC,MAAM,CAAC;QAEvD,OAAO,WAAW,CAAC;IACrB,CAAC;IAED,SAAS,CAAC,KAAa,EAAE,OAAwB,EAAE,IAAW,EAAE,OAAO,GAAG,IAAI,EAAE,WAAmB;QACjG,IAAI,KAAK,CAAC,QAAQ,CAAC,iBAAiB,IAAI,EAAE,CAAC;YAAE,OAAO,KAAK,CAAC;QAE1D,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,IAAI,IAAI,WAAW,OAAO,EAAE,GAAG,EAAE;YAChE,MAAM,OAAO,GAAG,WAAW,KAAK,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,WAAW,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;YAE5I,IAAI,CAAC,OAAO,CAAC,MAAM;gBAAE,OAAO,EAAE,CAAC;YAC/B,OAAO,qBAAqB,GAAG,IAAI,GAAG,YAAY,GAAG,OAAO,GAAG,qBAAqB,GAAG,IAAI,GAAG,UAAU,CAAC;QAC3G,CAAC,CAAC,CAAC;QAEH,qDAAqD;QACrD,IAAI,CAAC,IAAI,CAAC,MAAM;YAAE,OAAO,KAAK,CAAC;QAE/B,OAAO,KAAK,CAAC,OAAO,CAAC,OAAO,EAAE,GAAG,CAAC,EAAE,GAAG,OAAO,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG,IAAI,CAAC,CAAC,CAAC,IAAI,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;IACtF,CAAC;IAED,IAAI,CAAC,IAAY,EAAE,MAAM,GAAG,EAAE,IAAI,EAAE,EAAE,EAAE;QACtC,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,CAAC;QACxB,MAAM,WAAW,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;QAE5C,IAAI,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,KAAK,CAAC,EAAE,CAAC;YACrC,oBAAoB;YACpB,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,WAAW,EAAE,YAAY,EAAE,IAAI,EAAE,WAAW,CAAC,CAAC;QAC5E,CAAC;QAED,IAAI,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,KAAK,CAAC,EAAE,CAAC;YACnC,kBAAkB;YAClB,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,SAAS,EAAE,UAAU,EAAE,KAAK,EAAE,WAAW,CAAC,CAAC;QACzE,CAAC;QAED,IAAI,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,KAAK,CAAC,EAAE,CAAC;YACrC,oBAAoB;YACpB,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,WAAW,EAAE,YAAY,EAAE,IAAI,EAAE,WAAW,CAAC,CAAC;QAC5E,CAAC;QAED,IAAI,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,KAAK,CAAC,EAAE,CAAC;YACnC,kBAAkB;YAClB,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,SAAS,EAAE,UAAU,EAAE,KAAK,EAAE,WAAW,CAAC,CAAC;QACzE,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;CACF;AAED,iBAAS,QAAQ,CAAC"}