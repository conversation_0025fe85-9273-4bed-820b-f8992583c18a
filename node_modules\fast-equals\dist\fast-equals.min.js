!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports):"function"==typeof define&&define.amd?define(["exports"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self)["fast-equals"]={})}(this,(function(e){"use strict";var t="function"==typeof WeakMap,n=Object.keys;function r(e,t){return e===t||e!=e&&t!=t}function o(e){return e.constructor===Object||null==e.constructor}function u(e){return!!e&&"function"==typeof e.then}function f(e){return!(!e||!e.$$typeof)}function i(){var e=[];return{delete:function(t){for(var n=0;n<e.length;++n)if(e[n][0]===t)return void e.splice(n,1)},get:function(t){for(var n=0;n<e.length;++n)if(e[n][0]===t)return e[n][1]},set:function(t,n){for(var r=0;r<e.length;++r)if(e[r][0]===t)return void(e[r][1]=n);e.push([t,n])}}}var a=t?function(){return new WeakMap}:i;function c(e){return function(t){var n=e||t;return function(e,t,r,o,u,f,i){void 0===i&&(i=a());var c=!!e&&"object"==typeof e,l=!!t&&"object"==typeof t;if(c!==l)return!1;if(!c&&!l)return n(e,t,i);var s=i.get(e);if(s&&i.get(t))return s===t;i.set(e,t),i.set(t,e);var p=n(e,t,i);return i.delete(e),i.delete(t),p}}}var l=Function.prototype.bind.call(Function.prototype.call,Object.prototype.hasOwnProperty);function s(e,t,r,o){var u=n(e),i=u.length;if(n(t).length!==i)return!1;if(i)for(var a=void 0;i-- >0;){if("_owner"===(a=u[i])){var c=f(e),s=f(t);if((c||s)&&c!==s)return!1}if(!l(t,a)||!r(e[a],t[a],a,a,e,t,o))return!1}return!0}var p="g"===/foo/g.flags?function(e,t){return e.source===t.source&&e.flags===t.flags}:function(e,t){return e.source===t.source&&e.global===t.global&&e.ignoreCase===t.ignoreCase&&e.multiline===t.multiline&&e.unicode===t.unicode&&e.sticky===t.sticky&&e.lastIndex===t.lastIndex};var v="function"==typeof Map,y="function"==typeof Set,g=Object.prototype.valueOf;function d(e){var t="function"==typeof e?e(n):function(e,t,r,o,u,f,i){return n(e,t,i)};function n(e,n,f){if(e===n)return!0;if(e&&n&&"object"==typeof e&&"object"==typeof n){if(o(e)&&o(n))return s(e,n,t,f);var i=Array.isArray(e),a=Array.isArray(n);return i||a?i===a&&function(e,t,n,r){var o=e.length;if(t.length!==o)return!1;for(;o-- >0;)if(!n(e[o],t[o],o,o,e,t,r))return!1;return!0}(e,n,t,f):(i=e instanceof Date,a=n instanceof Date,i||a?i===a&&r(e.getTime(),n.getTime()):(i=e instanceof RegExp,a=n instanceof RegExp,i||a?i===a&&p(e,n):u(e)||u(n)?e===n:v&&(i=e instanceof Map,a=n instanceof Map,i||a)?i===a&&function(e,t,n,r){var o=e.size===t.size;if(o&&e.size){var u={},f=0;e.forEach((function(i,a){if(o){var c=!1,l=0;t.forEach((function(o,s){c||u[l]||(c=n(a,s,f,l,e,t,r)&&n(i,o,a,s,e,t,r))&&(u[l]=!0),l++})),f++,o=c}}))}return o}(e,n,t,f):y&&(i=e instanceof Set,a=n instanceof Set,i||a)?i===a&&function(e,t,n,r){var o=e.size===t.size;if(o&&e.size){var u={};e.forEach((function(f,i){if(o){var a=!1,c=0;t.forEach((function(o,l){a||u[c]||(a=n(f,o,i,l,e,t,r))&&(u[c]=!0),c++})),o=a}}))}return o}(e,n,t,f):e.valueOf!==g||n.valueOf!==g?r(e.valueOf(),n.valueOf()):s(e,n,t,f)))}return e!=e&&n!=n}return n}var h=d(),b=d((function(){return r})),E=d(c()),O=d(c(r));e.circularDeepEqual=E,e.circularShallowEqual=O,e.createCustomEqual=d,e.deepEqual=h,e.sameValueZeroEqual=r,e.shallowEqual=b,Object.defineProperty(e,"__esModule",{value:!0})}));
