.about {
  padding: 15px;
  background-color: #fff;
  border-radius: 5px;
  box-shadow: 0 0 5px #333;
}
.about-top {
  padding-bottom: 20px;
  border-bottom: 1px solid #eee;
}
.about-title {
  position: relative;
  z-index: 2;
}
.about-title h2 {
  position: relative;
  z-index: 2;
  color: #1e90ff;
}
.about-title::after {
  content: '';
  position: absolute;
  width: 20%;
  height: 50%;
  left: 0;
  bottom: 0;
  background: linear-gradient(to right, #87ceeb, #f0f8ff);
  border-radius: 0 3px 3px 0;
  z-index: 0;
}
.about-info {
  margin-top: 20px;
  display: flex;
  align-items: center;
}
.about-avatar {
  width: 124px;
  height: 124px;
  border-radius: 50%;
  border: 1px solid #ccc;
  padding: 5px;
  box-shadow: 0 0 5px #999;
}
.about-detail {
  display: flex;
  margin-left: 20px;
  flex-wrap: wrap;
  border: 1px solid #eee;
  border-radius: 5px;
  padding: 10px;
  box-shadow: 0 0 5px inset #ccc;
  position: relative;
}
.about-detail::after {
  content: '';
  position: absolute;
  display: block;
  width: 10px;
  height: 10px;
  left: -15px;
  top: 50%;
  transform: translateY(-50%);
  border-radius: 50%;
  box-shadow: 0 0 5px inset #ccc;
}
.about-detail p {
  margin: 5px 10px;
  font-size: 14px;
  color: #333;
}
.about-content {
  padding: 15px 0;
}
