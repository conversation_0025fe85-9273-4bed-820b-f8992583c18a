<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  
  <title>我的技术与生活——LibreTV影视 | Hexo</title>
  <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
  <link rel="shortcut icon" href="/imgs/shortcut-icon.ico" type="image/x-icon">
  <link rel="stylesheet" href="/css/public.css" />
  <link rel="stylesheet" href="/css/layout.css" />
  <link rel="stylesheet" href="/css/iconfont.css" />
  <link rel="stylesheet" href="/css/APlayer.min.css" />
  <script src="/js/APlayer.min.js"></script>
  <script src="/js/jquery.min.js"></script>
  <script src="/js/jquery.pjax.min.js"></script>

  <script src='//unpkg.com/valine/dist/Valine.min.js'></script>
  <script>
    document.title = `我的技术与生活——LibreTV影视`
  </script>
<meta name="generator" content="Hexo 7.3.0"></head>

<style>
  .load {
    width: 100%;
    height: 100vh;
    background-color: rgb(37, 35, 40);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    position: relative;
    z-index: 9999;
  }
  .load-circle {
    width: 80px;
    height: 80px;
    border: 8px solid orange;
    border-bottom-color: transparent;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    animation: rotate 1s linear infinite;
    filter: drop-shadow(0 0 3px orange);
  }
  .load-circle-inner {
    width: 40px;
    height: 40px;
    border: 8px solid orange;
    border-top-color: transparent;
    border-radius: 50%;
    animation: rotate-reverse .5s linear infinite;
  }
  .load-text {
    margin-top: 20px;
    font-size: 24px;
    color: orange;
    display: flex;
  }
  .load-text span {
    margin: 0 5px;
    text-shadow: 5px 5px 5px orange;
    animation: move 1s linear infinite;
  }
  .load-text span:nth-child(1) {
    animation-delay: -0.6s;
  }
  .load-text span:nth-child(2) {
    animation-delay: -0.5s;
  }
  .load-text span:nth-child(3) {
    animation-delay: -0.4s;
  }
  .load-text span:nth-child(4) {
    animation-delay: -0.3s;
  }
  .load-text span:nth-child(5) {
    animation-delay: -0.2s;
  }
  .load-text span:nth-child(6) {
    animation-delay: -0.1s;
  }
  @keyframes rotate {
    0% { transform: rotate(0); }
    100% { transform: rotate(360deg); }
  }
  @keyframes rotate-reverse {
    0% { transform: rotate(0); }
    100% { transform: rotate(-360deg); }
  }
  @keyframes move {
    0% { transform: translateY(0%) rotate(0) scale(1); }
    20% { transform: translateY(20%) rotate(10deg) scale(1.2); }
    80% { transform: translateY(-10%) rotate(-20deg) scale(.8);}
    100% { transform: translateY(0) rotate(0) scale(1); }
  }

  .progress {
    position: fixed;
    left: 0; top: 0;
    width: 0;
    height: 3px;
    background-color: green;
    transition: all cubic-bezier(0.215, 0.610, 0.355, 1) .1s;
    z-index: 9999;
  }

  .to-up {
    animation: toUp .5s 1;
  }
  .gray {
    position: fixed;
    left: 0;
    top: 0;
    width: 100%;
    height: 100vh;
    z-index: 9999;
    display: none;
    pointer-events: none;
    background-color: #000;
    mix-blend-mode: color;
  }
  @keyframes toUp {
    from { transform: translateY(15px); opacity: 0; }
    to { transform: translateY(0) ; opacity: 1; }
  }
</style>
<body>
  <div id="load" class="load">
    <div class="load-circle">
      <div class="load-circle-inner"></div>
    </div>
    <p class="load-text">
      <span>L</span>
      <span>O</span>
      <span>A</span>
      <span>D</span>
      <span>I</span>
      <span>N</span>
      <span>G</span>
    </p>
  </div>
  <div id="container" class="container w-100 vh-100" style="display: none;">
    <header class="header">
  <div class="header-wrapper">
    <div class="header-left">
      <div class="header-search">
        <input id="search-input" type="text" class="header-search--input" placeholder="请输入要检索的文章标题" />
        <span id="search-btn" class="header-search--icon"><i class="iconfont icon-sousuo"></i></span>
      </div>
      <div id="search-layer" class="header-search--layer hidden">
        <p class="title">
          <span>以下是搜索内容：</span>
          <span id="close-layer-btn">关闭</span>
        </p>
        <ul>
        </ul>
      </div>
    </div>
    <div class="header-right">
      <ul class="header-menu">
        <li>
          <a href="http://example.com/">
            <i class="header-menu--icon iconfont icon-shouye"></i>
            <span class="header-menu--span">首页</span>
          </a>
        </li>
        <li>
          <a href="http://example.com/log">
            <i class="header-menu--icon iconfont icon-rizhi"></i>
            <span class="header-menu--span">日志</span>
          </a>
        </li>
        <li>
          <a href="http://example.com/link">
            <i class="header-menu--icon iconfont icon-youqinglianjie"></i>
            <span class="header-menu--span">友情链接</span>
          </a>
        </li>
        <li>
          <a href="http://example.com/about">
            <i class="header-menu--icon iconfont icon-guanyuwomen"></i>
            <span class="header-menu--span">关于我</span>
          </a>
        </li>
      </ul>
    </div>
  </div>
</header>

<script>
  const ipt = document.querySelector('#search-input')
  const btn = document.querySelector('#search-btn')
  const layer = document.querySelector('#search-layer')
  const posts = JSON.parse(`[{"title":"LibreTV影视","cover":"https://img.0407123.xyz/%E5%BD%B1%E8%A7%86.png","path":"2024/12/19/libretv-yingshi/"}]`)
  ipt.addEventListener('keyup', e => {
    if (e.key === 'Enter') {
      handleSearch()
    }
  })
  btn.addEventListener('click', () => {
    handleSearch()
  })

  document.querySelector('#close-layer-btn').addEventListener('click', () => {
    layer.classList.toggle('hidden')
  })

  function handleSearch() {
    if (ipt.value.trim() === '') {
      return
    }
    let html = ''
    const targetPosts = posts.filter(post => post.title.includes(ipt.value))
    targetPosts.forEach(post => {
      html += `
        <li>
          <div>
            <a href="/${post.path}">${post.title.replace(new RegExp(ipt.value), `<span>${ipt.value}</span>`)}</a>
          </div>
          <img src="${post.cover || '/imgs/default-cover.webp' }" />
        </li>
      `
    })
    if (html.trim () === '') {
      html += '<p class="empty">没有搜索到内容</p>'
    }
    layer.querySelector('ul').innerHTML = html
    layer.classList.remove('hidden')
  }
</script> 
    <section id="main" class="main">
      <div class="main-left-wrapper">
<div class="main-left">
  <div class="main-left--block">
    <div class="main-left--info">
      <img src="/imgs/avatar.jpg"" class="main-left--avatar" />
      <div class="main-left--intro">
        <p class="main-left--name">Cola.</p>
        <div class="main-left--tags">
          <span class="main-left--tag">中二</span>
          <span class="main-left--tag">宅</span>
        </div>
      </div>
    </div>
  
    <div>
      <div class="main-left--motto">
        <p>“花有重开日，人无再少年”</p>
        <p>“一个简单普通的男孩”</p>
      </div>
      <div class="main-left--github">
        <span class="line"></span>
        <a target="_blank" rel="noopener" href="https://github.com/Aizener"><i class="logo iconfont icon-github-fill"></i></a>
        <span class="line"></span>
      </div>
      <div class="main-left--statics">
        <a href="/categories">
          <div>
            <span>1</span>
            <span>分类</span>
          </div>
        </a>
        <a href="/tags">
          <div>
            <span>3</span>
            <span>标签</span>
          </div>
        </a>
        <a href="/archives">
          <div>
            <span>1 </span>
            <span>归档</span>
          </div>
        </a>
      </div>
    </div>
  </div>

  <div class="main-left--block">
    <ul class="main-left--menu">
      
        <li>
          <a href="/">
            <span class="header-menu--span">小站首页</span>
            <i class="header-menu--icon iconfont icon-shouye"></i>
          </a>
        </li>
      
        <li>
          <a href="/log">
            <span class="header-menu--span">个人日志</span>
            <i class="header-menu--icon iconfont icon-rizhi"></i>
          </a>
        </li>
      
        <li>
          <a href="/link">
            <span class="header-menu--span">友情链接</span>
            <i class="header-menu--icon iconfont icon-youqinglianjie"></i>
          </a>
        </li>
      
        <li>
          <a href="/about">
            <span class="header-menu--span">关于自己</span>
            <i class="header-menu--icon iconfont icon-guanyuwomen"></i>
          </a>
        </li>
      
        <li>
          <a href="/categories">
            <span class="header-menu--span">分类</span>
            <i class="header-menu--icon iconfont icon-fenlei"></i>
          </a>
        </li>
      
        <li>
          <a href="/tags">
            <span class="header-menu--span">标签</span>
            <i class="header-menu--icon iconfont icon-biaoqian"></i>
          </a>
        </li>
      
        <li>
          <a href="/tools">
            <span class="header-menu--span">我的工具</span>
            <i class="header-menu--icon iconfont icon-gongju"></i>
          </a>
        </li>
      
    </ul>
  </div>

  <div class="main-left--block">
    <div class="main-left--site">
      <h5 class="main-left--title">
        <span>站点信息</span>
        <i class="iconfont icon-zhandian"></i>
      </h5>
      <p class="main-left--subtitle">
        <span>文章数目：</span>
        <span>1 篇</span>
      </p>
      <p class="main-left--subtitle">
        <span>最近动态：</span>
        <span>今天</span>
      </p>
      <p class="main-left--subtitle">
        <span>上线时间：</span>
        <span>1206天</span>
      </p>
      <p class="main-left--subtitle">
        <span>当前版本：</span>
        <span>v1.0.2</span>
      </p>
    </div>
  </div>
</div></div>
      <div id="main-container" class="main-container">


  <link rel="stylesheet" href="/css/partial/article.css" />

<div class="article-container">
  <div class="article">
    <h1 class="article-title">LibreTV影视</h1>
    <div class="article-info">
      <div class="article-info--item">
        <div class="article-info--info">
          
          <div class="article-info--categories">
            <span>分类：</span>
            <a class="category-link" href="/categories/%E5%BD%B1%E8%A7%86%E8%B5%84%E6%BA%90/">影视资源</a>
          </div>
          
          
          <div class="article-info--tags">
            <span>标签：</span>
            <a class="tag-link" href="/tags/LibreTV/" rel="tag">LibreTV</a><a class="tag-link" href="/tags/%E5%9C%A8%E7%BA%BF%E8%A7%82%E7%9C%8B/" rel="tag">在线观看</a><a class="tag-link" href="/tags/%E5%BD%B1%E8%A7%86/" rel="tag">影视</a>
          </div>
          
          <p class="article-info--date">日期：2024-12-19 10:00:00</p>
        </div>
        <img src="https://img.0407123.xyz/%E5%BD%B1%E8%A7%86.png" alt="" class="article-cover">
      </div>
    </div>
    <article class="article-content markdown-body">
      <h2 id="平台简介"><a href="#平台简介" class="headerlink" title="平台简介"></a>平台简介</h2><p><img src="https://img.0407123.xyz/%E5%BD%B1%E8%A7%86.png" alt="LibreTV影视平台"></p>
<p>LibreTV影视是一个优质的在线影视观看平台，为用户提供丰富的影视资源和流畅的观看体验。平台界面简洁美观，操作便捷，是影视爱好者的不错选择。该平台采用现代化设计理念，致力于为用户打造最佳的在线观影体验。</p>
<h2 id="访问信息"><a href="#访问信息" class="headerlink" title="访问信息"></a>访问信息</h2><ul>
<li><strong>官方网址</strong>: <a target="_blank" rel="noopener" href="http://ys.0407123.xyz/">http://ys.0407123.xyz/</a></li>
<li><strong>访问密码</strong>: <code>admin123</code></li>
<li><strong>平台类型</strong>: 在线影视观看平台</li>
<li><strong>支持设备</strong>: PC、手机、平板等多终端</li>
</ul>
<blockquote>
<p>💡 <strong>重要提示</strong>: 首次访问需要输入密码，请妥善保存访问密码。建议收藏网址以便后续快速访问。</p>
</blockquote>
<h2 id="平台特色"><a href="#平台特色" class="headerlink" title="平台特色"></a>平台特色</h2><h3 id="🎬-海量影视资源库"><a href="#🎬-海量影视资源库" class="headerlink" title="🎬 海量影视资源库"></a>🎬 海量影视资源库</h3><ul>
<li><strong>最新院线电影</strong> - 同步更新热门大片</li>
<li><strong>经典影片收藏</strong> - 怀旧经典，重温美好</li>
<li><strong>热播剧集追更</strong> - 国内外热门电视剧</li>
<li><strong>精彩综艺节目</strong> - 娱乐综艺，轻松一刻</li>
<li><strong>动漫二次元</strong> - 日漫国漫，应有尽有</li>
<li><strong>纪录片专区</strong> - 知识科普，开阔视野</li>
</ul>
<h3 id="🚀-卓越观看体验"><a href="#🚀-卓越观看体验" class="headerlink" title="🚀 卓越观看体验"></a>🚀 卓越观看体验</h3><ul>
<li><strong>4K超高清画质</strong> - 支持多种分辨率选择</li>
<li><strong>智能码率适配</strong> - 根据网络自动调节</li>
<li><strong>极速缓冲加载</strong> - 先进CDN技术支持</li>
<li><strong>无广告干扰</strong> - 纯净观影环境</li>
<li><strong>断点续播功能</strong> - 随时继续观看进度</li>
</ul>
<h3 id="📱-全平台兼容"><a href="#📱-全平台兼容" class="headerlink" title="📱 全平台兼容"></a>📱 全平台兼容</h3><ul>
<li><strong>PC端完美支持</strong> - Windows&#x2F;Mac&#x2F;Linux</li>
<li><strong>移动端优化</strong> - iOS&#x2F;Android友好界面</li>
<li><strong>平板设备适配</strong> - iPad等大屏设备</li>
<li><strong>智能电视支持</strong> - 客厅大屏观影</li>
<li><strong>响应式设计</strong> - 自适应各种屏幕尺寸</li>
</ul>
<h2 id="界面预览"><a href="#界面预览" class="headerlink" title="界面预览"></a>界面预览</h2><p><img src="https://img.0407123.xyz/%E5%BD%B1%E8%A7%86%E7%95%8C%E9%9D%A2.png" alt="LibreTV影视界面截图"></p>
<h3 id="界面设计亮点"><a href="#界面设计亮点" class="headerlink" title="界面设计亮点"></a>界面设计亮点</h3><p>从界面截图可以看出，LibreTV影视采用了现代化的扁平设计风格，具有以下特点：</p>
<ul>
<li><strong>简洁明了的导航栏</strong> - 功能分区清晰，操作直观</li>
<li><strong>精美的海报展示</strong> - 高质量影片封面，视觉效果佳</li>
<li><strong>智能分类布局</strong> - 按类型、地区、年份等多维度分类</li>
<li><strong>搜索功能突出</strong> - 便于快速查找目标内容</li>
<li><strong>响应式布局</strong> - 适配不同设备屏幕尺寸</li>
<li><strong>夜间模式支持</strong> - 保护用户视力，提升观影体验</li>
</ul>
<h2 id="使用体验"><a href="#使用体验" class="headerlink" title="使用体验"></a>使用体验</h2><h3 id="优点"><a href="#优点" class="headerlink" title="优点"></a>优点</h3><ul>
<li>✅ 界面设计美观，用户体验良好</li>
<li>✅ 影视资源更新及时</li>
<li>✅ 播放稳定，加载速度快</li>
<li>✅ 分类清晰，便于查找内容</li>
<li>✅ 支持多种设备访问</li>
</ul>
<h3 id="注意事项"><a href="#注意事项" class="headerlink" title="注意事项"></a>注意事项</h3><ul>
<li>🔐 需要访问密码，请妥善保管</li>
<li>📶 建议在良好的网络环境下使用</li>
<li>💻 推荐使用现代浏览器访问以获得最佳体验</li>
</ul>
<h2 id="功能亮点"><a href="#功能亮点" class="headerlink" title="功能亮点"></a>功能亮点</h2><h3 id="智能搜索"><a href="#智能搜索" class="headerlink" title="智能搜索"></a>智能搜索</h3><p>平台提供强大的搜索功能，用户可以通过关键词快速找到想要观看的影视内容。支持按演员、导演、类型等多维度搜索。</p>
<h3 id="个性化推荐"><a href="#个性化推荐" class="headerlink" title="个性化推荐"></a>个性化推荐</h3><p>基于用户观看历史和偏好，平台会智能推荐相关的影视内容，帮助用户发现更多感兴趣的作品。</p>
<h3 id="收藏功能"><a href="#收藏功能" class="headerlink" title="收藏功能"></a>收藏功能</h3><p>用户可以将喜欢的影视作品添加到收藏夹，方便后续观看和管理。</p>
<h3 id="观看历史"><a href="#观看历史" class="headerlink" title="观看历史"></a>观看历史</h3><p>自动记录用户的观看历史，支持断点续播，让用户可以随时继续之前未看完的内容。</p>
<h2 id="内容分类"><a href="#内容分类" class="headerlink" title="内容分类"></a>内容分类</h2><h3 id="电影专区"><a href="#电影专区" class="headerlink" title="电影专区"></a>电影专区</h3><ul>
<li>院线新片</li>
<li>经典老片</li>
<li>国产电影</li>
<li>欧美大片</li>
<li>日韩电影</li>
<li>其他地区电影</li>
</ul>
<h3 id="电视剧专区"><a href="#电视剧专区" class="headerlink" title="电视剧专区"></a>电视剧专区</h3><ul>
<li>国产剧集</li>
<li>韩剧</li>
<li>日剧</li>
<li>美剧</li>
<li>英剧</li>
<li>泰剧</li>
</ul>
<h3 id="综艺节目"><a href="#综艺节目" class="headerlink" title="综艺节目"></a>综艺节目</h3><ul>
<li>真人秀</li>
<li>音乐节目</li>
<li>脱口秀</li>
<li>游戏竞技</li>
<li>生活服务</li>
</ul>
<h3 id="动漫天地"><a href="#动漫天地" class="headerlink" title="动漫天地"></a>动漫天地</h3><ul>
<li>国产动漫</li>
<li>日本动漫</li>
<li>欧美动画</li>
<li>动画电影</li>
</ul>
<h2 id="技术特点"><a href="#技术特点" class="headerlink" title="技术特点"></a>技术特点</h2><h3 id="流媒体技术"><a href="#流媒体技术" class="headerlink" title="流媒体技术"></a>流媒体技术</h3><p>采用先进的流媒体技术，确保视频播放的流畅性和稳定性，支持自适应码率调节。</p>
<h3 id="CDN加速"><a href="#CDN加速" class="headerlink" title="CDN加速"></a>CDN加速</h3><p>利用全球CDN网络，为用户提供就近访问服务，大幅提升加载速度和观看体验。</p>
<h3 id="多格式支持"><a href="#多格式支持" class="headerlink" title="多格式支持"></a>多格式支持</h3><p>支持多种视频格式和编码，确保不同来源的影视内容都能正常播放。</p>
<h2 id="使用建议"><a href="#使用建议" class="headerlink" title="使用建议"></a>使用建议</h2><ol>
<li><strong>网络环境</strong>: 建议在稳定的网络环境下使用，以获得最佳观看体验</li>
<li><strong>浏览器选择</strong>: 推荐使用Chrome、Firefox、Safari等现代浏览器</li>
<li><strong>设备兼容</strong>: 支持PC、平板、手机等多种设备访问</li>
<li><strong>账号管理</strong>: 妥善保管访问密码，避免泄露给他人</li>
</ol>
<h2 id="总结"><a href="#总结" class="headerlink" title="总结"></a>总结</h2><p>LibreTV影视作为一个在线影视平台，在资源丰富度、用户体验和技术稳定性方面都表现不错。平台界面设计现代化，功能完善，操作简便，为用户提供了良好的影视观看体验。对于喜欢在线观看影视内容的用户来说，是一个值得尝试的平台。</p>
<p>无论是追剧党、电影爱好者，还是综艺粉丝，都能在LibreTV影视找到适合自己的内容。平台的持续更新和优化，也确保了用户能够及时观看到最新的影视作品。</p>
<hr>
<p><strong>免责声明</strong>: 本文仅为平台介绍，请用户在使用时遵守相关法律法规，支持正版内容。</p>

    </article>
    
    <div class="read-nums">
      <!-- id 将作为查询条件 -->
      <span id="2024/12/19/libretv-yingshi/" class="leancloud_visitors" data-flag-title="Your Article Title">
        <em class="post-meta-item-text">浏览量</em>
        <i class="leancloud-visitors-count"></i>
      </span>
    </div>
    <div class="comments-intro">
      <h2>评论区</h2>
      <p>欢迎你留下宝贵的意见，昵称输入QQ号会显示QQ头像哦~</p>
    </div>
    <div id="vcomments" class="vcomments"></div>
    
  </div>
  <div class="article-catelogue">
    <div class="article-catelogue--wrapper">
      <div class="catelogue catelogue-1">
        <h3>目录</h3>
        <ol class="toc"><li class="toc-item toc-level-2"><a class="toc-link" href="#%E5%B9%B3%E5%8F%B0%E7%AE%80%E4%BB%8B"><span class="toc-number">1.</span> <span class="toc-text">平台简介</span></a></li><li class="toc-item toc-level-2"><a class="toc-link" href="#%E8%AE%BF%E9%97%AE%E4%BF%A1%E6%81%AF"><span class="toc-number">2.</span> <span class="toc-text">访问信息</span></a></li><li class="toc-item toc-level-2"><a class="toc-link" href="#%E5%B9%B3%E5%8F%B0%E7%89%B9%E8%89%B2"><span class="toc-number">3.</span> <span class="toc-text">平台特色</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#%F0%9F%8E%AC-%E6%B5%B7%E9%87%8F%E5%BD%B1%E8%A7%86%E8%B5%84%E6%BA%90%E5%BA%93"><span class="toc-number">3.1.</span> <span class="toc-text">🎬 海量影视资源库</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#%F0%9F%9A%80-%E5%8D%93%E8%B6%8A%E8%A7%82%E7%9C%8B%E4%BD%93%E9%AA%8C"><span class="toc-number">3.2.</span> <span class="toc-text">🚀 卓越观看体验</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#%F0%9F%93%B1-%E5%85%A8%E5%B9%B3%E5%8F%B0%E5%85%BC%E5%AE%B9"><span class="toc-number">3.3.</span> <span class="toc-text">📱 全平台兼容</span></a></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#%E7%95%8C%E9%9D%A2%E9%A2%84%E8%A7%88"><span class="toc-number">4.</span> <span class="toc-text">界面预览</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#%E7%95%8C%E9%9D%A2%E8%AE%BE%E8%AE%A1%E4%BA%AE%E7%82%B9"><span class="toc-number">4.1.</span> <span class="toc-text">界面设计亮点</span></a></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#%E4%BD%BF%E7%94%A8%E4%BD%93%E9%AA%8C"><span class="toc-number">5.</span> <span class="toc-text">使用体验</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#%E4%BC%98%E7%82%B9"><span class="toc-number">5.1.</span> <span class="toc-text">优点</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#%E6%B3%A8%E6%84%8F%E4%BA%8B%E9%A1%B9"><span class="toc-number">5.2.</span> <span class="toc-text">注意事项</span></a></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#%E5%8A%9F%E8%83%BD%E4%BA%AE%E7%82%B9"><span class="toc-number">6.</span> <span class="toc-text">功能亮点</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#%E6%99%BA%E8%83%BD%E6%90%9C%E7%B4%A2"><span class="toc-number">6.1.</span> <span class="toc-text">智能搜索</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#%E4%B8%AA%E6%80%A7%E5%8C%96%E6%8E%A8%E8%8D%90"><span class="toc-number">6.2.</span> <span class="toc-text">个性化推荐</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#%E6%94%B6%E8%97%8F%E5%8A%9F%E8%83%BD"><span class="toc-number">6.3.</span> <span class="toc-text">收藏功能</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#%E8%A7%82%E7%9C%8B%E5%8E%86%E5%8F%B2"><span class="toc-number">6.4.</span> <span class="toc-text">观看历史</span></a></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#%E5%86%85%E5%AE%B9%E5%88%86%E7%B1%BB"><span class="toc-number">7.</span> <span class="toc-text">内容分类</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#%E7%94%B5%E5%BD%B1%E4%B8%93%E5%8C%BA"><span class="toc-number">7.1.</span> <span class="toc-text">电影专区</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#%E7%94%B5%E8%A7%86%E5%89%A7%E4%B8%93%E5%8C%BA"><span class="toc-number">7.2.</span> <span class="toc-text">电视剧专区</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#%E7%BB%BC%E8%89%BA%E8%8A%82%E7%9B%AE"><span class="toc-number">7.3.</span> <span class="toc-text">综艺节目</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#%E5%8A%A8%E6%BC%AB%E5%A4%A9%E5%9C%B0"><span class="toc-number">7.4.</span> <span class="toc-text">动漫天地</span></a></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#%E6%8A%80%E6%9C%AF%E7%89%B9%E7%82%B9"><span class="toc-number">8.</span> <span class="toc-text">技术特点</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#%E6%B5%81%E5%AA%92%E4%BD%93%E6%8A%80%E6%9C%AF"><span class="toc-number">8.1.</span> <span class="toc-text">流媒体技术</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#CDN%E5%8A%A0%E9%80%9F"><span class="toc-number">8.2.</span> <span class="toc-text">CDN加速</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#%E5%A4%9A%E6%A0%BC%E5%BC%8F%E6%94%AF%E6%8C%81"><span class="toc-number">8.3.</span> <span class="toc-text">多格式支持</span></a></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#%E4%BD%BF%E7%94%A8%E5%BB%BA%E8%AE%AE"><span class="toc-number">9.</span> <span class="toc-text">使用建议</span></a></li><li class="toc-item toc-level-2"><a class="toc-link" href="#%E6%80%BB%E7%BB%93"><span class="toc-number">10.</span> <span class="toc-text">总结</span></a></li></ol>
      </div>
      
    </div>
  </div>
</div>


<script>
  // var定义，避免pjax重新进来造成的重复声明错误
  var config = JSON.parse('{"enable":true,"appId":"Pf8zCXGEH1qsprnWfikVVujL-gzGzoHsz","appKey":"qOqoiUHhH1TGtLRUYURkLRQX","placeholder":"请留下你宝贵的意见吧~","meta":["nick"],"recordIP":true,"visitor":true,"enableQQ":true}')
  new Valine({
    el: '#vcomments',
    appId: config.appId,
    appKey: config.appKey,
    placeholder: config.placeholder,
    meta: config.meta,
    recordIP: config.recordIP,
    visitor: config.visitor,
    enableQQ: config.enableQQ,
    path: '2024/12/19/libretv-yingshi/'
  })
</script>


<script>
  $(document).on('pjax:complete', function() {
    const tocs = document.querySelector('.toc')
    const links = tocs ? tocs.querySelectorAll('a') : []
    links.forEach(link => {
      link.addEventListener('click', e => {
        const href = decodeURIComponent(e.href)
        href.search(/#(.*)/)
        const id = RegExp.$1
        const target = document.querySelector('#' + id)
        const top = target.offsetTop
        document.documentElement.scrollTo({
          top: top - 100,
          behavior: 'smooth'
        })
        e.preventDefault()
      })
    })
  })
</script> 

</div>
      <div class="main-right-wrapper"><div class="main-right">
  <div class="main-right--board">
    <div class="main-right--title">
      <h5>公告栏</h5>
      <i class="iconfont icon-gonggao"></i>
    </div>
    <div class="main-right--content">
      Hello~大噶好。唔系可乐爱宅着，欢迎你们来到我的博客小站，希望能在这里收获到有用的东西哦！ 
    </div>
  </div>

  <div id="aplayer" class="main-right--music"></div>

  <div class="operate-items">
    <div class="operate-item backtop">
      <i class="iconfont icon-huidaodingbu"></i>
      <span>回到顶部</span>
    </div>
    
    <div class="operate-item turn-comment hidden">
      <i class="iconfont icon-pinglun"></i>
      <span>查看评论</span>
    </div>
    
  </div>

  <div class="main-right--site">
    <div class="main-right--power">
      <p>Power By <a target="_blank" rel="noopener" href="https://hexo.io/zh-cn/docs/">Hexo</a>.</p>
      <p>Theme：<a target="_blank" rel="noopener" href="https://github.com/Aizener/hexo-theme-cola">Cola.</a></p>
    </div>
    <p class="main-right--refer"><a target="_blank" rel="noopener" href="https://beian.miit.gov.cn/#/Integrated/index">蜀ICP备2022005384号-1</a> </p>
  </div>
</div>

<script>
  function setOperateItem () {
    const reg = /\d{4}\/\d{2}\/\d{2}\/.+/
    const path = location.pathname
    const operateDom = document.querySelector('.main-right .operate-items')
    const commentDom = document.querySelector('.turn-comment')
    const cateloguDom = document.querySelector('.article-catelogue > .article-catelogue--wrapper');

    if (commentDom) {
      if (reg.test(path) || path.match(/\/log\/.+/)) {
        commentDom.classList.remove('hidden')
        const newDom = operateDom.cloneNode(true);
        const _backtopDom = newDom.querySelector('.backtop');
        const _commentDom = newDom.querySelector('.turn-comment');
        _backtopDom.addEventListener('click', () => backTopEvent());
        _commentDom.addEventListener('click', () => commentDomEvent());
        cateloguDom.appendChild(newDom);
      } else {
        commentDom.classList.add('hidden')
      }
    }
  }

  setOperateItem()
  const musics = JSON.parse(`[{"name":"安河桥","artist":"宋冬野","url":"http://ting6.yymp3.net:82/new25/songdongye/11.mp3","cover":"https://img2.baidu.com/it/u=1260056724,1076343118&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=500"},{"name":"卡布达","artist":"暂无","url":"music/kabuda.mp3","cover":"https://img2.baidu.com/it/u=705831265,2862720033&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=500"}]`)
  const ap = new APlayer({
    container: document.querySelector('#aplayer'),
    audio: musics,
  })

  $(document).on('pjax:complete', function() {
    setOperateItem()
  })

  document.querySelector('.backtop').addEventListener('click', () => {
    backTopEvent();
  })
  const dom = document.querySelector('.turn-comment')
  dom && dom.addEventListener('click', () => {
    commentDomEvent();
  })

  function backTopEvent() {
    document.documentElement.scrollTo({
      top: 0,
      behavior: 'smooth'
    })
  }

  function commentDomEvent() {
    const commentDom = document.querySelector('.comments-intro')
    if (!commentDom) return
    const top = commentDom.offsetTop, height = commentDom.offsetHeight
    document.documentElement.scrollTo({
      top: top - 2 * height,
      behavior: 'smooth'
    })
  }
</script></div>
    </section>
  </div>
  <div id="progress" class="progress"></div>
  <div id="gray" class="gray"></div>

  <script>
    function initScroll () {
      document.addEventListener('scroll', () => {
        const doc = document.documentElement
        const scrollTop = doc.scrollTop
        const pageHeight = doc.offsetHeight
        const clientHeight = doc.clientHeight
        const ratio = scrollTop / (pageHeight - clientHeight)
        const progress = document.querySelector('#progress')
        const avatarImg = document.querySelector('.main-left--avatar')
        progress.style.width = (100 * ratio) + '%'
        avatarImg.style.transform = `rotate(${360 * ratio}deg)`
      })
    }

    const rootPath = "/"

    const checkAndSetArticlePageLayout = () => {
      const path = location.pathname.replace(rootPath, '');
      if (
        /^\/?\d{4}\/\d{2}\/\d{2}\/.*/.test(path) ||
        /^log\/.+/.test(path)
      ) {
        $('.main-container, .main-right, .main-right-wrapper').addClass('is-article')
      } else {
        $('.main-container, .main-right, .main-right-wrapper').removeClass('is-article')
      }
    }

    const gray = "none"
    const setGrayStyle = () => {
      if (gray === 'none') {
        return
      } else if (gray === 'index') {
        location.pathname === '/' ? $('#gray').show() : $('#gray').hide()
      } else if (gray === 'all') {
        $('#gray').show()
      }
    }
    setGrayStyle()


    window.onload = function () {
      checkAndSetArticlePageLayout()
      setTimeout(() => {
        $('#load').slideUp()
        $('#container').slideToggle()
        setTimeout(() => {
          initScroll();
        }, 500)
      }, 500)
    }
    
    let status = 0
    // 对所有链接跳转事件绑定pjax容器container
    $(document).pjax('a[target!=_blank]', '#main-container', {
      container: '#main-container',
      fragment: '#main-container',
      timeout: 8000
    })

    $(document).on('pjax:start', function() {
    })
    $(document).on('pjax:complete', function() {
      status = 0
      $('.main-container').addClass('to-up').on('animationend', function() {
        $(this).removeClass('to-up')
      })
      setGrayStyle()
      checkAndSetArticlePageLayout()
    })
    $(document).on('pjax:popstate', function() {
      status = -1
      checkAndSetArticlePageLayout()
    });
  </script>
</body>
</html>