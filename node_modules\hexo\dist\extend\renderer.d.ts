import Promise from 'bluebird';
import type { NodeJSLikeCallback } from '../types';
export interface StoreFunctionData {
    path?: any;
    text?: string;
    engine?: string;
    toString?: any;
    onRenderEnd?: (...args: any[]) => any;
}
export interface StoreSyncFunction {
    [x: string]: any;
    (data: StoreFunctionData, options: object): any;
    output?: string;
    compile?: (local: object) => any;
}
export interface StoreFunction {
    (data: StoreFunctionData, options: object, callback?: NodeJSLikeCallback<any>): Promise<any>;
    (data: StoreFunctionData, options: object, callback: NodeJSLikeCallback<string>): void;
    output?: string;
    compile?: (local: object) => any;
    disableNunjucks?: boolean;
}
interface SyncStore {
    [key: string]: StoreSyncFunction;
}
interface Store {
    [key: string]: StoreFunction;
}
declare class Renderer {
    store: Store;
    storeSync: SyncStore;
    constructor();
    list(sync?: boolean): Store | SyncStore;
    get(name: string, sync?: boolean): StoreSyncFunction | StoreFunction;
    isRenderable(path: string): boolean;
    isRenderableSync(path: string): boolean;
    getOutput(path: string): string;
    register(name: string, output: string, fn: StoreFunction): void;
    register(name: string, output: string, fn: StoreFunction, sync: false): void;
    register(name: string, output: string, fn: StoreSyncFunction, sync: true): void;
    register(name: string, output: string, fn: StoreFunction | StoreSyncFunction, sync: boolean): void;
}
export default Renderer;
