{"version": 3, "file": "tag.js", "sourceRoot": "", "sources": ["../../lib/extend/tag.ts"], "names": [], "mappings": ";;;;AAAA,yCAAwC;AACxC,2CAAsD;AACtD,uCAAuC;AACvC,wDAA+B;AAG/B,MAAM,iBAAiB,GAAG,aAAa,CAAC;AACxC,MAAM,QAAQ,GAAG,+BAA+B,CAAC;AACjD,MAAM,aAAa,GAAG,CAAC,GAAW,EAAE,EAAE,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC,OAAO,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;AAS3F,MAAM,WAAW;IAIf,YAAY,IAAY,EAAE,EAAkC;QAC1D,IAAI,CAAC,IAAI,GAAG,CAAC,IAAI,CAAC,CAAC;QACnB,IAAI,CAAC,EAAE,GAAG,EAAE,CAAC;IACf,CAAC;IAED,KAAK,CAAC,MAAM,EAAE,KAAK,EAAE,KAAK;QACxB,MAAM,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;QAEnD,OAAO,IAAI,KAAK,CAAC,aAAa,CAAC,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,EAAE,CAAC,CAAC;IACxD,CAAC;IAED,UAAU,CAAC,MAAM,EAAE,KAAK,EAAE,KAAK;QAC7B,MAAM,GAAG,GAAG,MAAM,CAAC,SAAS,EAAE,CAAC;QAC/B,MAAM,IAAI,GAAG,IAAI,KAAK,CAAC,QAAQ,CAAC,GAAG,CAAC,MAAM,EAAE,GAAG,CAAC,KAAK,CAAC,CAAC;QACvD,MAAM,QAAQ,GAAG,IAAI,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,EAAE,GAAG,CAAC,KAAK,CAAC,CAAC;QAExD,IAAI,KAAK,CAAC;QACV,IAAI,OAAO,GAAG,EAAE,CAAC;QAEjB,OAAO,CAAC,KAAK,GAAG,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC;YACxC,IAAI,KAAK,CAAC,IAAI,KAAK,KAAK,CAAC,gBAAgB,IAAI,KAAK,CAAC,IAAI,KAAK,KAAK,CAAC,eAAe,EAAE,CAAC;gBAClF,IAAI,OAAO,KAAK,EAAE,EAAE,CAAC;oBACnB,MAAM,OAAO,GAAG,IAAI,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,EAAE,GAAG,CAAC,KAAK,EAAE,OAAO,CAAC,IAAI,EAAE,CAAC,CAAC;oBACzE,QAAQ,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;oBAC3B,OAAO,GAAG,EAAE,CAAC;gBACf,CAAC;gBAED,IAAI,KAAK,CAAC,IAAI,KAAK,KAAK,CAAC,eAAe,EAAE,CAAC;oBACzC,MAAM;gBACR,CAAC;YACH,CAAC;iBAAM,CAAC;gBACN,OAAO,IAAI,KAAK,CAAC,KAAK,CAAC;YACzB,CAAC;QACH,CAAC;QAED,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;QAExB,OAAO,IAAI,CAAC;IACd,CAAC;IAED,GAAG,CAAC,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,QAAQ;QAC/B,OAAO,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,EAAE,EAAE,CAAC,CAAC;IACtC,CAAC;IAED,IAAI,CAAC,OAAO,EAAE,IAAI,EAAE,IAAI;QACtB,OAAO,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,EAAE,OAAO,CAAC,GAAG,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC;IAC3D,CAAC;CACF;AAED,MAAM,QAAQ,GAAG,CAAC,IAAe,EAAE,EAAE;IACnC,OAAO,IAAA,uBAAW,EAAC,IAAI,EAAE,CAAC,CAAC,OAAO,CAAC,YAAY,EAAE,EAAE,CAAC,CAAC;AACvD,CAAC,CAAC;AAEF,MAAM,aAAc,SAAQ,WAAW;IACrC,KAAK,CAAC,MAAM,EAAE,KAAK,EAAE,KAAK;QACxB,MAAM,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;QACnD,MAAM,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;QAEnD,OAAO,IAAI,KAAK,CAAC,aAAa,CAAC,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC;IAC5D,CAAC;IAED,UAAU,CAAC,MAAM,EAAE,KAAK,EAAE,KAAK;QAC7B,MAAM,IAAI,GAAG,MAAM,CAAC,gBAAgB,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QAE3D,MAAM,CAAC,oBAAoB,EAAE,CAAC;QAC9B,OAAO,IAAI,CAAC;IACd,CAAC;IAED,GAAG,CAAC,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,QAAQ;QAC/B,OAAO,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,EAAE,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;IAClD,CAAC;CACF;AAED,MAAM,gBAAiB,SAAQ,WAAW;IACxC,KAAK,CAAC,MAAM,EAAE,KAAK,EAAE,KAAK;QACxB,MAAM,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;QAEnD,OAAO,IAAI,KAAK,CAAC,kBAAkB,CAAC,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,EAAE,CAAC,CAAC;IAC7D,CAAC;IAED,GAAG,CAAC,OAAO,EAAE,IAAI,EAAE,QAAQ;QACzB,OAAO,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,EAAE,EAAE,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE;YAChD,QAAQ,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;QACzB,CAAC,EAAE,QAAQ,CAAC,CAAC;IACf,CAAC;CACF;AAED,MAAM,kBAAmB,SAAQ,aAAa;IAC5C,KAAK,CAAC,MAAM,EAAE,KAAK,EAAE,KAAK;QACxB,MAAM,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;QACnD,MAAM,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;QAEnD,OAAO,IAAI,KAAK,CAAC,kBAAkB,CAAC,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC;IACjE,CAAC;IAED,GAAG,CAAC,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,QAAQ;QAC/B,2BAA2B;QAC3B,IAAI,CAAC,CAAC,GAAG,EAAE,MAAM,EAAE,EAAE;YACnB,iCAAiC;YACjC,wBAAwB;YACxB,IAAI,GAAG,GAAG,EAAE,CAAC,MAAM,IAAI,EAAE,CAAC;YAE1B,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,EAAE,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE;gBACrD,QAAQ,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC;YACxB,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC;CACF;AAED,MAAM,kBAAkB,GAAG,CAAC,GAAW,EAAE,GAAW,EAAE,MAAc,EAAE,SAAiB,EAAE,EAAE;IACzF,MAAM,MAAM,GAAG,EAAE,CAAC;IAClB,IAAI,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,MAAM,GAAG,SAAS,CAAC,CAAC;IAC/C,MAAM,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,MAAM,GAAG,SAAS,CAAC,CAAC;IACjD,OAAO,MAAM,IAAI,MAAM;QAAE,MAAM,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC;IAC/C,OAAO,MAAM,CAAC;AAChB,CAAC,CAAC;AAEF,MAAM,gBAAgB,GAAG,CAAC,CAAC;AAE3B,MAAM,UAAU,GAAG,CAAC,KAAe,EAAE,OAAe,EAAE,QAAgB,EAAE,IAAY,EAAE,EAAE;IACtF,MAAM,OAAO,GAAG;QACd,QAAQ,GAAG,GAAG,GAAG,IAAA,gBAAG,EAAC,IAAI,CAAC;QAC1B,IAAA,iBAAI,EAAC,0DAA0D,CAAC;QAChE,IAAA,iBAAI,EAAC,0DAA0D,CAAC;KACjE,CAAC;IAEF,OAAO,CAAC,IAAI;IACV,mDAAmD;IACnD,GAAG,kBAAkB,CAAC,CAAC,EAAE,KAAK,CAAC,MAAM,EAAE,OAAO,EAAE,gBAAgB,CAAC;SAC9D,GAAG,CAAC,KAAK,CAAC,EAAE;QACX,MAAM,IAAI,GAAG,IAAI,GAAG,KAAK,GAAG,KAAK,GAAG,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;QACrD,IAAI,KAAK,KAAK,OAAO,EAAE,CAAC;YACtB,OAAO,IAAA,iBAAI,EAAC,IAAA,iBAAI,EAAC,IAAI,CAAC,CAAC,CAAC;QAC1B,CAAC;QAED,OAAO,IAAA,iBAAI,EAAC,IAAI,CAAC,CAAC;IACpB,CAAC,CAAC,CACL,CAAC;IACF,OAAO,CAAC,IAAI,CAAC,IAAA,iBAAI,EACf,0DAA0D,CAAC,CAAC,CAAC;IAE/D,OAAO,OAAO,CAAC;AACjB,CAAC,CAAC;AAEF,MAAM,aAAc,SAAQ,KAAK;CAIhC;AAED;;;;;GAKG;AACH,MAAM,mBAAmB,GAAG,CAAC,GAAU,EAAE,KAAa,EAAE,MAAM,GAAG,EAAE,EAAS,EAAE;IAC5E,GAAG,CAAC,OAAO,GAAG,GAAG,CAAC,OAAO,CAAC,OAAO,CAAC,gBAAgB,EAAE,MAAM,CAAC,CAAC,CAAC,IAAA,oBAAO,EAAC,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IAEnF,MAAM,KAAK,GAAG,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,wBAAwB,CAAC,CAAC;IAC1D,IAAI,CAAC,KAAK;QAAE,OAAO,GAAG,CAAC;IACvB,MAAM,OAAO,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;IACvC,IAAI,KAAK,CAAC,OAAO,CAAC;QAAE,OAAO,GAAG,CAAC;IAE/B,wCAAwC;IACxC,MAAM,QAAQ,GAAG,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;IAEzC,MAAM,CAAC,GAAG,IAAI,aAAa,EAAE,CAAC;IAC9B,CAAC,CAAC,IAAI,GAAG,gBAAgB,CAAC;IAC1B,CAAC,CAAC,IAAI,GAAG,OAAO,CAAC;IACjB,CAAC,CAAC,QAAQ,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;IACzB,CAAC,CAAC,IAAI,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;IAC5B,CAAC,CAAC,OAAO,GAAG,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE,OAAO,EAAE,CAAC,CAAC,QAAQ,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IACrF,OAAO,CAAC,CAAC;AACX,CAAC,CAAC;AAOF,MAAM,GAAG;IAIP;QACE,IAAI,CAAC,GAAG,GAAG,IAAI,sBAAW,CAAC,IAAI,EAAE;YAC/B,UAAU,EAAE,KAAK;SAClB,CAAC,CAAC;IACL,CAAC;IAKD,QAAQ,CAAC,IAAY,EAAE,EAAe,EAAE,OAAmC;QACzE,IAAI,CAAC,IAAI;YAAE,MAAM,IAAI,SAAS,CAAC,kBAAkB,CAAC,CAAC;QACnD,IAAI,OAAO,EAAE,KAAK,UAAU;YAAE,MAAM,IAAI,SAAS,CAAC,uBAAuB,CAAC,CAAC;QAE3E,IAAI,OAAO,IAAI,IAAI,IAAI,OAAO,OAAO,KAAK,SAAS,EAAE,CAAC;YACpD,OAAO,GAAG,EAAE,IAAI,EAAE,OAAkB,EAAE,CAAC;QACzC,CAAC;QAED,IAAI,GAAgB,CAAC;QAErB,IAAI,OAAO,CAAC,KAAK,EAAE,CAAC;YAClB,IAAI,OAAyB,CAAC;YAC9B,IAAI,EAAE,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAClB,OAAO,GAAG,kBAAO,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC;YAClC,CAAC;iBAAM,CAAC;gBACN,OAAO,GAAG,kBAAO,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;YAC/B,CAAC;YAED,IAAI,OAAO,CAAC,IAAI,EAAE,CAAC;gBACjB,GAAG,GAAG,IAAI,kBAAkB,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;YAC9C,CAAC;iBAAM,CAAC;gBACN,GAAG,GAAG,IAAI,gBAAgB,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;YAC5C,CAAC;QACH,CAAC;aAAM,IAAI,OAAO,CAAC,IAAI,EAAE,CAAC;YACxB,GAAG,GAAG,IAAI,aAAa,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;QACpC,CAAC;aAAM,CAAC;YACN,GAAG,GAAG,IAAI,WAAW,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;QAClC,CAAC;QAED,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;IACnC,CAAC;IAED,UAAU,CAAC,IAAY;QACrB,IAAI,CAAC,IAAI;YAAE,MAAM,IAAI,SAAS,CAAC,kBAAkB,CAAC,CAAC;QAEnD,MAAM,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;QAErB,IAAI,GAAG,CAAC,YAAY,CAAC,IAAI,CAAC;YAAE,GAAG,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;IACxD,CAAC;IAKD,MAAM,CAAC,GAAW,EAAE,UAA6E,EAAE,EAAE,QAAkC;QACrI,IAAI,CAAC,QAAQ,IAAI,OAAO,OAAO,KAAK,UAAU,EAAE,CAAC;YAC/C,QAAQ,GAAG,OAAO,CAAC;YACnB,OAAO,GAAG,EAAE,CAAC;QACf,CAAC;QAED,+BAA+B;QAC/B,MAAM,EAAE,MAAM,GAAG,EAAE,EAAE,GAAG,OAA8B,CAAC;QAEvD,OAAO,kBAAO,CAAC,YAAY,CAAC,EAAE,CAAC,EAAE;YAC/B,IAAI,CAAC,GAAG,CAAC,YAAY,CACnB,GAAG,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC,CAAC,EAAE;gBACxB,uCAAuC;gBACvC,yDAAyD;gBACzD,2DAA2D;gBAC3D,OAAO,CAAC,CAAC,KAAK,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC;YAC3D,CAAC,CAAC,EACF,OAAO,EACP,EAAE,CACH,CAAC;QACJ,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE;YACb,OAAO,kBAAO,CAAC,MAAM,CAAC,mBAAmB,CAAC,GAAG,EAAE,GAAG,EAAE,MAAM,CAAC,CAAC,CAAC;QAC/D,CAAC,CAAC;aACC,UAAU,CAAC,QAAQ,CAAC,CAAC;IAC1B,CAAC;CACF;AAED,iBAAS,GAAG,CAAC"}