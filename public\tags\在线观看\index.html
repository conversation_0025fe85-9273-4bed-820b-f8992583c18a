<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  
  <title>我的技术与生活——小站首页 | Hexo</title>
  <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
  <link rel="shortcut icon" href="/imgs/shortcut-icon.ico" type="image/x-icon">
  <link rel="stylesheet" href="/css/public.css" />
  <link rel="stylesheet" href="/css/layout.css" />
  <link rel="stylesheet" href="/css/iconfont.css" />
  <link rel="stylesheet" href="/css/APlayer.min.css" />
  <script src="/js/APlayer.min.js"></script>
  <script src="/js/jquery.min.js"></script>
  <script src="/js/jquery.pjax.min.js"></script>

  <script src='//unpkg.com/valine/dist/Valine.min.js'></script>
  <script>
    document.title = `我的技术与生活——小站首页`
  </script>
<meta name="generator" content="Hexo 7.3.0"></head>

<style>
  .load {
    width: 100%;
    height: 100vh;
    background-color: rgb(37, 35, 40);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    position: relative;
    z-index: 9999;
  }
  .load-circle {
    width: 80px;
    height: 80px;
    border: 8px solid orange;
    border-bottom-color: transparent;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    animation: rotate 1s linear infinite;
    filter: drop-shadow(0 0 3px orange);
  }
  .load-circle-inner {
    width: 40px;
    height: 40px;
    border: 8px solid orange;
    border-top-color: transparent;
    border-radius: 50%;
    animation: rotate-reverse .5s linear infinite;
  }
  .load-text {
    margin-top: 20px;
    font-size: 24px;
    color: orange;
    display: flex;
  }
  .load-text span {
    margin: 0 5px;
    text-shadow: 5px 5px 5px orange;
    animation: move 1s linear infinite;
  }
  .load-text span:nth-child(1) {
    animation-delay: -0.6s;
  }
  .load-text span:nth-child(2) {
    animation-delay: -0.5s;
  }
  .load-text span:nth-child(3) {
    animation-delay: -0.4s;
  }
  .load-text span:nth-child(4) {
    animation-delay: -0.3s;
  }
  .load-text span:nth-child(5) {
    animation-delay: -0.2s;
  }
  .load-text span:nth-child(6) {
    animation-delay: -0.1s;
  }
  @keyframes rotate {
    0% { transform: rotate(0); }
    100% { transform: rotate(360deg); }
  }
  @keyframes rotate-reverse {
    0% { transform: rotate(0); }
    100% { transform: rotate(-360deg); }
  }
  @keyframes move {
    0% { transform: translateY(0%) rotate(0) scale(1); }
    20% { transform: translateY(20%) rotate(10deg) scale(1.2); }
    80% { transform: translateY(-10%) rotate(-20deg) scale(.8);}
    100% { transform: translateY(0) rotate(0) scale(1); }
  }

  .progress {
    position: fixed;
    left: 0; top: 0;
    width: 0;
    height: 3px;
    background-color: green;
    transition: all cubic-bezier(0.215, 0.610, 0.355, 1) .1s;
    z-index: 9999;
  }

  .to-up {
    animation: toUp .5s 1;
  }
  .gray {
    position: fixed;
    left: 0;
    top: 0;
    width: 100%;
    height: 100vh;
    z-index: 9999;
    display: none;
    pointer-events: none;
    background-color: #000;
    mix-blend-mode: color;
  }
  @keyframes toUp {
    from { transform: translateY(15px); opacity: 0; }
    to { transform: translateY(0) ; opacity: 1; }
  }
</style>
<body>
  <div id="load" class="load">
    <div class="load-circle">
      <div class="load-circle-inner"></div>
    </div>
    <p class="load-text">
      <span>L</span>
      <span>O</span>
      <span>A</span>
      <span>D</span>
      <span>I</span>
      <span>N</span>
      <span>G</span>
    </p>
  </div>
  <div id="container" class="container w-100 vh-100" style="display: none;">
    <header class="header">
  <div class="header-wrapper">
    <div class="header-left">
      <div class="header-search">
        <input id="search-input" type="text" class="header-search--input" placeholder="请输入要检索的文章标题" />
        <span id="search-btn" class="header-search--icon"><i class="iconfont icon-sousuo"></i></span>
      </div>
      <div id="search-layer" class="header-search--layer hidden">
        <p class="title">
          <span>以下是搜索内容：</span>
          <span id="close-layer-btn">关闭</span>
        </p>
        <ul>
        </ul>
      </div>
    </div>
    <div class="header-right">
      <ul class="header-menu">
        <li>
          <a href="http://example.com/">
            <i class="header-menu--icon iconfont icon-shouye"></i>
            <span class="header-menu--span">首页</span>
          </a>
        </li>
        <li>
          <a href="http://example.com/log">
            <i class="header-menu--icon iconfont icon-rizhi"></i>
            <span class="header-menu--span">日志</span>
          </a>
        </li>
        <li>
          <a href="http://example.com/link">
            <i class="header-menu--icon iconfont icon-youqinglianjie"></i>
            <span class="header-menu--span">友情链接</span>
          </a>
        </li>
        <li>
          <a href="http://example.com/about">
            <i class="header-menu--icon iconfont icon-guanyuwomen"></i>
            <span class="header-menu--span">关于我</span>
          </a>
        </li>
      </ul>
    </div>
  </div>
</header>

<script>
  const ipt = document.querySelector('#search-input')
  const btn = document.querySelector('#search-btn')
  const layer = document.querySelector('#search-layer')
  const posts = JSON.parse(`[{"title":"LibreTV影视","path":"2024/12/19/libretv-yingshi/"}]`)
  ipt.addEventListener('keyup', e => {
    if (e.key === 'Enter') {
      handleSearch()
    }
  })
  btn.addEventListener('click', () => {
    handleSearch()
  })

  document.querySelector('#close-layer-btn').addEventListener('click', () => {
    layer.classList.toggle('hidden')
  })

  function handleSearch() {
    if (ipt.value.trim() === '') {
      return
    }
    let html = ''
    const targetPosts = posts.filter(post => post.title.includes(ipt.value))
    targetPosts.forEach(post => {
      html += `
        <li>
          <div>
            <a href="/${post.path}">${post.title.replace(new RegExp(ipt.value), `<span>${ipt.value}</span>`)}</a>
          </div>
          <img src="${post.cover || '/imgs/default-cover.webp' }" />
        </li>
      `
    })
    if (html.trim () === '') {
      html += '<p class="empty">没有搜索到内容</p>'
    }
    layer.querySelector('ul').innerHTML = html
    layer.classList.remove('hidden')
  }
</script> 
    <section id="main" class="main">
      <div class="main-left-wrapper">
<div class="main-left">
  <div class="main-left--block">
    <div class="main-left--info">
      <img src="/imgs/avatar.jpg"" class="main-left--avatar" />
      <div class="main-left--intro">
        <p class="main-left--name">Cola.</p>
        <div class="main-left--tags">
          <span class="main-left--tag">中二</span>
          <span class="main-left--tag">宅</span>
        </div>
      </div>
    </div>
  
    <div>
      <div class="main-left--motto">
        <p>“花有重开日，人无再少年”</p>
        <p>“一个简单普通的男孩”</p>
      </div>
      <div class="main-left--github">
        <span class="line"></span>
        <a target="_blank" rel="noopener" href="https://github.com/Aizener"><i class="logo iconfont icon-github-fill"></i></a>
        <span class="line"></span>
      </div>
      <div class="main-left--statics">
        <a href="/categories">
          <div>
            <span>1</span>
            <span>分类</span>
          </div>
        </a>
        <a href="/tags">
          <div>
            <span>3</span>
            <span>标签</span>
          </div>
        </a>
        <a href="/archives">
          <div>
            <span>1 </span>
            <span>归档</span>
          </div>
        </a>
      </div>
    </div>
  </div>

  <div class="main-left--block">
    <ul class="main-left--menu">
      
        <li>
          <a href="/">
            <span class="header-menu--span">小站首页</span>
            <i class="header-menu--icon iconfont icon-shouye"></i>
          </a>
        </li>
      
        <li>
          <a href="/log">
            <span class="header-menu--span">个人日志</span>
            <i class="header-menu--icon iconfont icon-rizhi"></i>
          </a>
        </li>
      
        <li>
          <a href="/link">
            <span class="header-menu--span">友情链接</span>
            <i class="header-menu--icon iconfont icon-youqinglianjie"></i>
          </a>
        </li>
      
        <li>
          <a href="/about">
            <span class="header-menu--span">关于自己</span>
            <i class="header-menu--icon iconfont icon-guanyuwomen"></i>
          </a>
        </li>
      
        <li>
          <a href="/categories">
            <span class="header-menu--span">分类</span>
            <i class="header-menu--icon iconfont icon-fenlei"></i>
          </a>
        </li>
      
        <li>
          <a href="/tags">
            <span class="header-menu--span">标签</span>
            <i class="header-menu--icon iconfont icon-biaoqian"></i>
          </a>
        </li>
      
        <li>
          <a href="/tools">
            <span class="header-menu--span">我的工具</span>
            <i class="header-menu--icon iconfont icon-gongju"></i>
          </a>
        </li>
      
    </ul>
  </div>

  <div class="main-left--block">
    <div class="main-left--site">
      <h5 class="main-left--title">
        <span>站点信息</span>
        <i class="iconfont icon-zhandian"></i>
      </h5>
      <p class="main-left--subtitle">
        <span>文章数目：</span>
        <span>1 篇</span>
      </p>
      <p class="main-left--subtitle">
        <span>最近动态：</span>
        <span>今天</span>
      </p>
      <p class="main-left--subtitle">
        <span>上线时间：</span>
        <span>1206天</span>
      </p>
      <p class="main-left--subtitle">
        <span>当前版本：</span>
        <span>v1.0.2</span>
      </p>
    </div>
  </div>
</div></div>
      <div id="main-container" class="main-container"><link rel="stylesheet" href="/css/partial/index.css" />



<div class="index">
  <div class="index-left">
    
      <div class="index-article">
        <!-- <img class="index-article--cover" src="/imgs/default-cover.webp" alt=""> -->
        <div class="index-article--box">
          <img class="index-article--cover" data-cover="imgs/default-cover.webp" alt="LibreTV影视" />
        </div>
        <div class="index-article--content">
          <a href="/2024/12/19/libretv-yingshi/">LibreTV影视 </a>
          <div>
            
              <p>
                <a class="post-category-link" href="/categories/%E5%BD%B1%E8%A7%86%E8%B5%84%E6%BA%90/">影视资源</a>
              </p>
            
            
              <p>
                <a class="-link" href="/tags/%E5%BD%B1%E8%A7%86/" rel="tag">影视</a><span> ◆ </span><a class="-link" href="/tags/%E5%9C%A8%E7%BA%BF%E8%A7%82%E7%9C%8B/" rel="tag">在线观看</a><span> ◆ </span><a class="-link" href="/tags/LibreTV/" rel="tag">LibreTV</a>
              </p>
            
          </div>
          <div class="bg"></div>
        </div>
      </div>
    
  </div>
  <div class="index-center">
    
  </div>
  <div class="index-right">
    <div class="index-right-wrapper">
      <ul class="category-list"><li class="category-list-item"><a class="category-list-link" href="/categories/%E5%BD%B1%E8%A7%86%E8%B5%84%E6%BA%90/">影视资源</a></li></ul>
      <ul class="tags" itemprop="keywords"><li class="tag-list-item"><a class="tag-list-link" href="/tags/%E5%BD%B1%E8%A7%86/" rel="tag">影视</a></li><li class="tag-list-item"><a class="tag-list-link" href="/tags/%E5%9C%A8%E7%BA%BF%E8%A7%82%E7%9C%8B/" rel="tag">在线观看</a></li><li class="tag-list-item"><a class="tag-list-link" href="/tags/LibreTV/" rel="tag">LibreTV</a></li></ul>
      <ul class="archive-list"><li class="archive-list-item"><a class="archive-list-link" href="/archives/2024/12/">2024年12月</a><span class="archive-list-count">1</span></li></ul>
    </div>
  </div>
</div>


<script>
  var allImgBoxs = document.querySelectorAll('.index-article--box');
  var windowHeight = window.innerHeight;
  var boxs = Array.from(allImgBoxs);
  var timer = null;
  boxs.forEach((box, idx) => {
    if (!window.loadImageSet || !window.loadImageSet.includes(idx)) {
      box.classList.add('img-loading');
    } else {
      const imgDom = box.querySelector('img');
      imgDom.src = imgDom.dataset.cover;
    }
  });

  function scrollFn() {
    if (timer) {
      return;
    }
    timer = setTimeout(() => {
      loadImage();
      timer = null;
    }, 100)
  }

  setTimeout(() => {
    loadImage();
  }, 100);
  if (!window.loadedImage) {
    document.addEventListener('scroll', scrollFn);
  }

  function loadImage() {
    const doc = document.documentElement;
    const scrollTop = doc.scrollTop;
    if (boxs.every(box => box.loadedImage)) {
      document.removeEventListener('scroll', scrollFn);
      window.loadedImage = 1;
      return;
    }
    boxs.forEach((box, idx) => {
      if (box.loadedImage) {
        return;
      }
      if (box.getBoundingClientRect().top < windowHeight) {
        const imgDom = box.querySelector('img');
        imgDom.src = imgDom.dataset.cover;
        // 图片成功加载
        imgDom.onload = () => {
          box.classList.remove('img-loading');
          box.loadedImage = true;
          if (!window.loadImageSet) {
            window.loadImageSet = [];
          }
          window.loadImageSet.push(idx);
        }
        // 图片加载失败
        imgDom.onerror = () => {
          box.classList.remove('img-loading');
          box.classList.add('img-error');
          imgDom.src = '/imgs/404.png';
        }
      }
    });
  }
</script> </div>
      <div class="main-right-wrapper"><div class="main-right">
  <div class="main-right--board">
    <div class="main-right--title">
      <h5>公告栏</h5>
      <i class="iconfont icon-gonggao"></i>
    </div>
    <div class="main-right--content">
      Hello~大噶好。唔系可乐爱宅着，欢迎你们来到我的博客小站，希望能在这里收获到有用的东西哦！ 
    </div>
  </div>

  <div id="aplayer" class="main-right--music"></div>

  <div class="operate-items">
    <div class="operate-item backtop">
      <i class="iconfont icon-huidaodingbu"></i>
      <span>回到顶部</span>
    </div>
    
    <div class="operate-item turn-comment hidden">
      <i class="iconfont icon-pinglun"></i>
      <span>查看评论</span>
    </div>
    
  </div>

  <div class="main-right--site">
    <div class="main-right--power">
      <p>Power By <a target="_blank" rel="noopener" href="https://hexo.io/zh-cn/docs/">Hexo</a>.</p>
      <p>Theme：<a target="_blank" rel="noopener" href="https://github.com/Aizener/hexo-theme-cola">Cola.</a></p>
    </div>
    <p class="main-right--refer"><a target="_blank" rel="noopener" href="https://beian.miit.gov.cn/#/Integrated/index">蜀ICP备2022005384号-1</a> </p>
  </div>
</div>

<script>
  function setOperateItem () {
    const reg = /\d{4}\/\d{2}\/\d{2}\/.+/
    const path = location.pathname
    const operateDom = document.querySelector('.main-right .operate-items')
    const commentDom = document.querySelector('.turn-comment')
    const cateloguDom = document.querySelector('.article-catelogue > .article-catelogue--wrapper');

    if (commentDom) {
      if (reg.test(path) || path.match(/\/log\/.+/)) {
        commentDom.classList.remove('hidden')
        const newDom = operateDom.cloneNode(true);
        const _backtopDom = newDom.querySelector('.backtop');
        const _commentDom = newDom.querySelector('.turn-comment');
        _backtopDom.addEventListener('click', () => backTopEvent());
        _commentDom.addEventListener('click', () => commentDomEvent());
        cateloguDom.appendChild(newDom);
      } else {
        commentDom.classList.add('hidden')
      }
    }
  }

  setOperateItem()
  const musics = JSON.parse(`[{"name":"安河桥","artist":"宋冬野","url":"http://ting6.yymp3.net:82/new25/songdongye/11.mp3","cover":"https://img2.baidu.com/it/u=1260056724,1076343118&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=500"},{"name":"卡布达","artist":"暂无","url":"music/kabuda.mp3","cover":"https://img2.baidu.com/it/u=705831265,2862720033&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=500"}]`)
  const ap = new APlayer({
    container: document.querySelector('#aplayer'),
    audio: musics,
  })

  $(document).on('pjax:complete', function() {
    setOperateItem()
  })

  document.querySelector('.backtop').addEventListener('click', () => {
    backTopEvent();
  })
  const dom = document.querySelector('.turn-comment')
  dom && dom.addEventListener('click', () => {
    commentDomEvent();
  })

  function backTopEvent() {
    document.documentElement.scrollTo({
      top: 0,
      behavior: 'smooth'
    })
  }

  function commentDomEvent() {
    const commentDom = document.querySelector('.comments-intro')
    if (!commentDom) return
    const top = commentDom.offsetTop, height = commentDom.offsetHeight
    document.documentElement.scrollTo({
      top: top - 2 * height,
      behavior: 'smooth'
    })
  }
</script></div>
    </section>
  </div>
  <div id="progress" class="progress"></div>
  <div id="gray" class="gray"></div>

  <script>
    function initScroll () {
      document.addEventListener('scroll', () => {
        const doc = document.documentElement
        const scrollTop = doc.scrollTop
        const pageHeight = doc.offsetHeight
        const clientHeight = doc.clientHeight
        const ratio = scrollTop / (pageHeight - clientHeight)
        const progress = document.querySelector('#progress')
        const avatarImg = document.querySelector('.main-left--avatar')
        progress.style.width = (100 * ratio) + '%'
        avatarImg.style.transform = `rotate(${360 * ratio}deg)`
      })
    }

    const rootPath = "/"

    const checkAndSetArticlePageLayout = () => {
      const path = location.pathname.replace(rootPath, '');
      if (
        /^\/?\d{4}\/\d{2}\/\d{2}\/.*/.test(path) ||
        /^log\/.+/.test(path)
      ) {
        $('.main-container, .main-right, .main-right-wrapper').addClass('is-article')
      } else {
        $('.main-container, .main-right, .main-right-wrapper').removeClass('is-article')
      }
    }

    const gray = "none"
    const setGrayStyle = () => {
      if (gray === 'none') {
        return
      } else if (gray === 'index') {
        location.pathname === '/' ? $('#gray').show() : $('#gray').hide()
      } else if (gray === 'all') {
        $('#gray').show()
      }
    }
    setGrayStyle()


    window.onload = function () {
      checkAndSetArticlePageLayout()
      setTimeout(() => {
        $('#load').slideUp()
        $('#container').slideToggle()
        setTimeout(() => {
          initScroll();
        }, 500)
      }, 500)
    }
    
    let status = 0
    // 对所有链接跳转事件绑定pjax容器container
    $(document).pjax('a[target!=_blank]', '#main-container', {
      container: '#main-container',
      fragment: '#main-container',
      timeout: 8000
    })

    $(document).on('pjax:start', function() {
    })
    $(document).on('pjax:complete', function() {
      status = 0
      $('.main-container').addClass('to-up').on('animationend', function() {
        $(this).removeClass('to-up')
      })
      setGrayStyle()
      checkAndSetArticlePageLayout()
    })
    $(document).on('pjax:popstate', function() {
      status = -1
      checkAndSetArticlePageLayout()
    });
  </script>
</body>
</html>